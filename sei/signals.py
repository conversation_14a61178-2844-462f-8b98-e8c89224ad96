import hashlib
import json
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils import timezone
from .models import Protocolo


@receiver(post_save, sender=Protocolo)
def generate_protocolo_hash(sender, instance, created, **kwargs):
    """
    Generate a cryptographic hash for Protocolo instances on creation.

    This signal creates a SHA-256 hash using protocol data to ensure
    document authenticity and detect tampering. The hash is only generated
    on creation to maintain document integrity.

    Args:
        sender: The model class (Protocolo)
        instance: The actual instance being saved
        created: Bo<PERSON>an indicating if this is a new instance
        **kwargs: Additional keyword arguments
    """
    # Only generate hash on creation, not on updates
    if created and not instance.hash:
        hash_data = _build_hash_data(instance)
        instance.hash = _generate_sha256_hash(hash_data)

        # Save the instance with the generated hash
        # Use update_fields to avoid triggering the signal again
        instance.save(update_fields=['hash'])


def _build_hash_data(protocolo):
    """
    Build a dictionary of data to be used for hash generation.

    This function collects all relevant protocol data that should be
    included in the authenticity hash. The data is structured to be
    deterministic and comprehensive.

    Args:
        protocolo: Protocolo instance

    Returns:
        dict: Dictionary containing all data for hash generation
    """
    # Get usuario data safely
    usuario_data = {}
    if protocolo.usuario:
        usuario_data = {
            'id': str(protocolo.usuario.id),
            'nome': protocolo.usuario.nome or '',
            'email': protocolo.usuario.email or '',
            'ip_address': protocolo.usuario.ip_address or ''
        }

    # Get related object codes safely
    related_data = {
        'unidade_codigo': protocolo.unidade.codigo if protocolo.unidade else None,
        'interessado_codigo': protocolo.interessado.codigo if protocolo.interessado else None,
        'localizacao_codigo': protocolo.localizacao.codigo if protocolo.localizacao else None,
        'assunto_codigo': protocolo.assunto.codigo if protocolo.assunto else None,
        'local_codigo': protocolo.local.codigo if protocolo.local else None,
        'disciplina_codigo': protocolo.disciplina.codigo if protocolo.disciplina else None,
    }

    # Build comprehensive hash data
    hash_data = {
        'protocolo_id': str(protocolo.id),
        'doc_cod': protocolo.doc_cod or '',
        'doc_revisao': protocolo.doc_revisao or '',
        'doc_sei_num': protocolo.doc_sei_num or '',
        'servico_codigo': protocolo.servico_codigo or '',
        'servico_tipo': protocolo.servico_tipo or '',
        'usuario': usuario_data,
        'related_objects': related_data,
        'created_at': protocolo.created_at.isoformat() if protocolo.created_at else '',
        'timestamp': timezone.now().isoformat(),  # Additional timestamp for uniqueness
    }

    return hash_data


def _generate_sha256_hash(data):
    """
    Generate a SHA-256 hash from the provided data.

    This function converts the data to a JSON string with sorted keys
    to ensure deterministic output, then generates a SHA-256 hash.

    Args:
        data: Dictionary containing data to hash

    Returns:
        str: SHA-256 hash as hexadecimal string
    """
    # Convert data to JSON string with sorted keys for deterministic output
    json_string = json.dumps(data, sort_keys=True, ensure_ascii=False)

    # Encode to bytes and generate SHA-256 hash
    hash_object = hashlib.sha256(json_string.encode('utf-8'))

    # Return hexadecimal representation
    return hash_object.hexdigest()


def verify_protocolo_hash(protocolo):
    """
    Verify the authenticity of a Protocolo instance by regenerating its hash.

    This function can be used to check if a protocol has been tampered with
    by comparing the stored hash with a newly generated one.

    Args:
        protocolo: Protocolo instance to verify

    Returns:
        bool: True if hash matches (authentic), False if tampered
    """
    if not protocolo.hash:
        return False

    # Rebuild hash data (excluding timestamp for verification)
    hash_data = _build_hash_data(protocolo)
    # Remove the timestamp field for verification as it's creation-specific
    hash_data.pop('timestamp', None)

    # Generate new hash and compare
    new_hash = _generate_sha256_hash(hash_data)
    return protocolo.hash == new_hash