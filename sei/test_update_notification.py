"""
Test cases for SEI protocol update notification functionality.
"""

from django.test import TestCase
from django.template.loader import render_to_string
from unittest.mock import patch, MagicMock
from .models import Protocolo, <PERSON>quisitante
from .tasks import send_protocolo_update_notification, _prepare_update_email_context


class UpdateNotificationTaskTest(TestCase):
    """Test cases for update notification Celery task"""
    
    def setUp(self):
        """Set up test data"""
        self.requisitante = Requisitante.objects.create(
            nome='MARIA SANTOS SILVA',
            email='<EMAIL>',
            ip_address='*************'
        )
        
        self.protocolo = Protocolo.objects.create(
            usuario=self.requisitante,
            doc_cod='TEST-UPDATE-001-2024',
            servico_codigo='456',
            servico_tipo='REVISÃO TÉCNICA',
            doc_sei_num='12345',
            doc_url_cod='UPDATEPASS'
        )
    
    def test_prepare_update_email_context(self):
        """Test update email context preparation"""
        old_value = '12345'
        new_value = '67890-NEW'
        
        context = _prepare_update_email_context(self.protocolo, old_value, new_value)
        
        # Verify basic context data
        self.assertEqual(context['protocolo'], self.protocolo)
        self.assertEqual(context['doc_cod'], 'TEST-UPDATE-001-2024')
        self.assertEqual(context['usuario_nome'], 'MARIA SANTOS SILVA')
        self.assertEqual(context['usuario_email'], '<EMAIL>')
        
        # Verify update-specific data
        self.assertTrue(context['is_update_notification'])
        self.assertEqual(context['update_info']['old_value'], old_value)
        self.assertEqual(context['update_info']['new_value'], new_value)
        self.assertEqual(context['update_info']['field_name'], 'Número SEI')
        self.assertTrue(context['update_info']['has_change'])
        
        # Verify access password is included
        self.assertEqual(context['access_password'], 'UPDATEPASS')
    
    def test_prepare_update_email_context_no_change(self):
        """Test update email context when no change occurred"""
        same_value = '12345'
        
        context = _prepare_update_email_context(self.protocolo, same_value, same_value)
        
        # Should still create context but indicate no change
        self.assertFalse(context['update_info']['has_change'])
        self.assertEqual(context['update_info']['old_value'], same_value)
        self.assertEqual(context['update_info']['new_value'], same_value)
    
    def test_prepare_update_email_context_empty_values(self):
        """Test update email context with empty values"""
        context = _prepare_update_email_context(self.protocolo, '', 'NEW-VALUE')
        
        # Should handle empty values gracefully
        self.assertEqual(context['update_info']['old_value'], 'Não informado')
        self.assertEqual(context['update_info']['new_value'], 'NEW-VALUE')
        self.assertTrue(context['update_info']['has_change'])
    
    @patch('sei.tasks._send_update_email')
    def test_update_notification_task_success(self, mock_send_email):
        """Test successful update notification task execution"""
        # Mock successful email sending
        mock_send_email.return_value = {'success': True, 'email': '<EMAIL>'}
        
        # Execute the task
        result = send_protocolo_update_notification(str(self.protocolo.id), '12345', '67890')
        
        # Verify success
        self.assertTrue(result['success'])
        self.assertEqual(result['protocolo_id'], str(self.protocolo.id))
        self.assertEqual(result['email'], '<EMAIL>')
        self.assertEqual(result['old_value'], '12345')
        self.assertEqual(result['new_value'], '67890')
    
    def test_update_notification_task_nonexistent_protocolo(self):
        """Test update notification task with non-existent protocolo ID"""
        fake_id = '00000000-0000-0000-0000-000000000000'
        
        # Execute the task
        result = send_protocolo_update_notification(fake_id, 'old', 'new')
        
        # Verify failure
        self.assertFalse(result['success'])
        self.assertEqual(result['error'], 'Protocolo not found')
    
    def test_update_notification_task_no_email(self):
        """Test update notification task when requisitante has no email"""
        # Create protocolo without email
        protocolo_no_email = Protocolo.objects.create(
            usuario=None,
            doc_cod='NO-EMAIL-UPDATE-TEST'
        )
        
        # Execute the task
        result = send_protocolo_update_notification(str(protocolo_no_email.id), 'old', 'new')
        
        # Verify failure
        self.assertFalse(result['success'])
        self.assertEqual(result['error'], 'No valid email address')
    
    def test_update_email_templates_render_correctly(self):
        """Test that update email templates render without errors"""
        context = _prepare_update_email_context(self.protocolo, '12345', '67890-NEW')
        
        # Test HTML template
        try:
            html_content = render_to_string('sei/email/protocolo_update_notification.html', context)
            self.assertIn('TEST-UPDATE-001-2024', html_content)
            self.assertIn('MARIA SANTOS SILVA', html_content)
            self.assertIn('12345', html_content)  # Old value
            self.assertIn('67890-NEW', html_content)  # New value
            self.assertIn('Protocolo Atualizado', html_content)
        except Exception as e:
            self.fail(f"HTML template rendering failed: {str(e)}")
        
        # Test text template
        try:
            text_content = render_to_string('sei/email/protocolo_update_notification.txt', context)
            self.assertIn('TEST-UPDATE-001-2024', text_content)
            self.assertIn('MARIA SANTOS SILVA', text_content)
            self.assertIn('12345', text_content)  # Old value
            self.assertIn('67890-NEW', text_content)  # New value
            self.assertIn('PROTOCOLO SEI ATUALIZADO', text_content)
        except Exception as e:
            self.fail(f"Text template rendering failed: {str(e)}")


class UpdateNotificationIntegrationTest(TestCase):
    """Integration tests for update notification functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.requisitante = Requisitante.objects.create(
            nome='INTEGRATION TEST USER',
            email='<EMAIL>',
            ip_address='*************'
        )
        
        self.protocolo = Protocolo.objects.create(
            usuario=self.requisitante,
            doc_cod='INTEGRATION-TEST-2024',
            doc_url_cod='INTEGRPASS',
            doc_sei_num='ORIGINAL'
        )
    
    @patch('sei.tasks.send_protocolo_update_notification.delay')
    def test_update_notification_triggered_on_edit(self, mock_task):
        """Test that update notification task is triggered when field is edited"""
        from django.test import Client
        from django.urls import reverse
        
        client = Client()
        
        # Simulate authentication session
        session = client.session
        session['authenticated_protocolo_id'] = str(self.protocolo.id)
        session['access_timestamp'] = '2024-01-01T12:00:00+00:00'
        session.save()
        
        edit_url = reverse('sei:protocolo_secure_edit', kwargs={'protocolo_id': self.protocolo.id})
        
        # Submit edit form
        response = client.post(edit_url, {
            'doc_sei_num': 'UPDATED-VALUE'
        })
        
        # Verify task was triggered
        mock_task.assert_called_once_with(str(self.protocolo.id), 'ORIGINAL', 'UPDATED-VALUE')
    
    @patch('sei.tasks.send_protocolo_update_notification.delay')
    def test_update_notification_not_triggered_on_no_change(self, mock_task):
        """Test that update notification task is NOT triggered when no change occurs"""
        from django.test import Client
        from django.urls import reverse
        
        client = Client()
        
        # Simulate authentication session
        session = client.session
        session['authenticated_protocolo_id'] = str(self.protocolo.id)
        session['access_timestamp'] = '2024-01-01T12:00:00+00:00'
        session.save()
        
        edit_url = reverse('sei:protocolo_secure_edit', kwargs={'protocolo_id': self.protocolo.id})
        
        # Submit edit form with same value
        response = client.post(edit_url, {
            'doc_sei_num': 'ORIGINAL'  # Same as current value
        })
        
        # Verify task was NOT triggered
        mock_task.assert_not_called()


if __name__ == '__main__':
    print("Testing SEI update notification functionality...")
    print("Run with: python manage.py test sei.test_update_notification")
