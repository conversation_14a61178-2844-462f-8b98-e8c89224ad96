#!/usr/bin/env python
"""
Test script to verify timezone handling in secure access system.
Run this to test the timezone parsing functionality.
"""

import os
import sys
import django

# Add the project root to the Python path
sys.path.insert(0, '/home/<USER>/Projects/python/cciapp/cciapp')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cciapp.settings')
django.setup()

from django.utils import timezone
from datetime import datetime, timedelta
from sei.views import _parse_session_timestamp


def test_timezone_parsing():
    """Test the timezone parsing function with various inputs"""
    print("Testing timezone parsing functionality...")
    
    # Test 1: Current timezone-aware timestamp
    current_time = timezone.now()
    iso_string = current_time.isoformat()
    print(f"\nTest 1: Current time")
    print(f"Original: {current_time}")
    print(f"ISO string: {iso_string}")
    
    parsed = _parse_session_timestamp(iso_string)
    print(f"Parsed: {parsed}")
    print(f"Types: original={type(current_time)}, parsed={type(parsed)}")
    print(f"Timezone aware: original={current_time.tzinfo is not None}, parsed={parsed.tzinfo is not None}")
    
    if parsed:
        diff = current_time - parsed
        print(f"Time difference: {diff}")
        print(f"Difference < 1 second: {abs(diff.total_seconds()) < 1}")
    
    # Test 2: ISO string with Z suffix
    z_string = current_time.strftime('%Y-%m-%dT%H:%M:%S.%fZ')
    print(f"\nTest 2: Z suffix format")
    print(f"Z string: {z_string}")
    
    parsed_z = _parse_session_timestamp(z_string)
    print(f"Parsed Z: {parsed_z}")
    print(f"Timezone aware: {parsed_z.tzinfo is not None if parsed_z else 'None'}")
    
    # Test 3: Session timeout calculation
    print(f"\nTest 3: Session timeout calculation")
    old_time = current_time - timedelta(minutes=35)  # 35 minutes ago
    old_iso = old_time.isoformat()
    print(f"Old time (35 min ago): {old_time}")
    print(f"Old ISO: {old_iso}")
    
    parsed_old = _parse_session_timestamp(old_iso)
    if parsed_old:
        time_diff = current_time - parsed_old
        print(f"Time difference: {time_diff}")
        print(f"Expired (>30 min): {time_diff > timedelta(minutes=30)}")
    
    # Test 4: Invalid inputs
    print(f"\nTest 4: Invalid inputs")
    invalid_inputs = [None, "", "invalid", 123, "2023-13-45T25:70:80"]
    
    for invalid in invalid_inputs:
        result = _parse_session_timestamp(invalid)
        print(f"Input: {invalid} -> Result: {result}")
    
    print("\nTimezone parsing tests completed!")


def test_session_simulation():
    """Simulate the session flow"""
    print("\n" + "="*50)
    print("SIMULATING SESSION FLOW")
    print("="*50)
    
    # Simulate login
    login_time = timezone.now()
    session_timestamp = login_time.isoformat()
    print(f"1. Login time: {login_time}")
    print(f"2. Stored in session: {session_timestamp}")
    
    # Simulate immediate access (should work)
    print(f"\n3. Immediate access check:")
    parsed_login = _parse_session_timestamp(session_timestamp)
    if parsed_login:
        immediate_diff = timezone.now() - parsed_login
        print(f"   Time difference: {immediate_diff}")
        print(f"   Access allowed: {immediate_diff <= timedelta(minutes=30)}")
    
    # Simulate access after 25 minutes (should work)
    print(f"\n4. Access after 25 minutes:")
    future_time = login_time + timedelta(minutes=25)
    if parsed_login:
        future_diff = future_time - parsed_login
        print(f"   Time difference: {future_diff}")
        print(f"   Access allowed: {future_diff <= timedelta(minutes=30)}")
    
    # Simulate access after 35 minutes (should fail)
    print(f"\n5. Access after 35 minutes:")
    expired_time = login_time + timedelta(minutes=35)
    if parsed_login:
        expired_diff = expired_time - parsed_login
        print(f"   Time difference: {expired_diff}")
        print(f"   Access allowed: {expired_diff <= timedelta(minutes=30)}")
    
    print("\nSession simulation completed!")


if __name__ == "__main__":
    test_timezone_parsing()
    test_session_simulation()
    print(f"\nAll tests completed successfully!")
