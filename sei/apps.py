from django.apps import AppConfig


class SeiConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'sei'
    verbose_name = 'SEI - Sistema Eletrônico de Informações'

    def ready(self):
        """
        Import signals when the app is ready.

        This method is called when Django starts and ensures that
        all signals are properly registered and connected.
        """
        try:
            # Import signals to register them
            from . import signals
        except ImportError:
            # Handle case where signals module doesn't exist or has import errors
            pass