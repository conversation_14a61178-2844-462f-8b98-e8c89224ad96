from django.urls import path
from . import views

app_name = "sei"

urlpatterns = [
    # Form view
    path('', views.SeiFormView.as_view(), name='sei_form'),
    path('success/<uuid:protocolo_id>/', views.SeiSuccessView.as_view(), name='success'),

    # Protocolo listing and management
    path('protocolos/', views.ProtocoloListView.as_view(), name='protocolo_list'),
    path('protocolos/<uuid:protocolo_id>/', views.ProtocoloDetailView.as_view(), name='protocolo_detail'),
    path('protocolos/<uuid:protocolo_id>/edit/', views.ProtocoloEditView.as_view(), name='protocolo_edit'),
    path('protocolos/<uuid:protocolo_id>/delete/', views.ProtocoloDeleteView.as_view(), name='protocolo_delete'),

    # PDF generation
    path('protocolo/<uuid:protocolo_id>/pdf/', views.SeiProtocoloPdfView.as_view(), name='protocolo_pdf'),
    path('protocolo/<uuid:protocolo_id>/pdf/html/', views.SeiProtocoloPdfHtmlView.as_view(), name='protocolo_pdf_html'),

    # API endpoints for Select2 dropdowns
    path('api/unidade/', views.UnidadeSearchAPIView.as_view(), name='unidade_search'),
    path('api/interessado/', views.InteressadoSearchAPIView.as_view(), name='interessado_search'),
    path('api/localizacao/', views.LocalizacaoSearchAPIView.as_view(), name='localizacao_search'),
    path('api/assunto/', views.AssuntoSearchAPIView.as_view(), name='assunto_search'),
    path('api/local/', views.LocalSearchAPIView.as_view(), name='local_search'),
    path('api/disciplina/', views.DisciplinaSearchAPIView.as_view(), name='disciplina_search'),

    # Form submission
    path('api/submit/', views.SeiFormSubmitAPIView.as_view(), name='form_submit'),

    # Secure protocol access
    path('protocolo/access/', views.ProtocoloAccessView.as_view(), name='protocolo_access'),
    path('protocolo/<uuid:protocolo_id>/secure-edit/', views.ProtocoloSecureEditView.as_view(), name='protocolo_secure_edit'),
    path('protocolo/<uuid:protocolo_id>/secure-success/', views.ProtocoloSecureSuccessView.as_view(), name='protocolo_secure_success'),
]