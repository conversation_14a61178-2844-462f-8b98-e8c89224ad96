from django.views.generic import Template<PERSON>iew, ListView, DetailView, UpdateView, DeleteView
from django.db.models import Q
from django.db import transaction
from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect, get_object_or_404
from django.contrib import messages
from django.core.paginator import Paginator
from django.urls import reverse_lazy, reverse
from django.utils import timezone
from django.core.cache import cache
from django.views.decorators.csrf import csrf_protect
from django.utils.decorators import method_decorator
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
import secrets
import string
import logging
from datetime import datetime, timedelta
from .models import Unidade, Interessado, Localizacao, Assunto, Local, Disciplina, Requisitante, Protocolo
from .utils import render_to_pdf, generate_pdf_filename, get_pdf_context, validate_protocolo_access

logger = logging.getLogger(__name__)

class SeiFormView(TemplateView):
    template_name = 'sei/form.html'

class SeiSuccessView(TemplateView):
    template_name = 'sei/success.html'

    def dispatch(self, request, *args, **kwargs):
        """
        Override dispatch to validate protocolo_id early and store the protocolo object
        """
        protocolo_id = kwargs.get('protocolo_id')

        if not protocolo_id:
            from django.shortcuts import redirect
            from django.contrib import messages
            messages.error(request, 'ID do protocolo não fornecido.')
            return redirect('sei:sei_form')

        try:
            self.protocolo = Protocolo.objects.select_related(
                'usuario', 'unidade', 'interessado', 'localizacao',
                'assunto', 'local', 'disciplina'
            ).get(id=protocolo_id)
        except Protocolo.DoesNotExist:
            from django.shortcuts import redirect
            from django.contrib import messages
            messages.error(request, f'Protocolo com ID {protocolo_id} não encontrado.')
            return redirect('sei:sei_form')
        except Exception as e:
            from django.shortcuts import redirect
            from django.contrib import messages
            messages.error(request, f'Erro ao carregar protocolo: {str(e)}')
            return redirect('sei:sei_form')

        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['protocolo'] = self.protocolo
        return context

class UnidadeSearchAPIView(APIView):
    """
    Search Unidade model using __str__() method
    Returns first 10 records when no query, searches when query provided
    """
    def get(self, request, format=None):
        query = request.GET.get('q', '').strip()

        if not query:
            # Return first 10 records when no search query
            unidades = Unidade.objects.all()[:10]
        else:
            # Search all fields using the string representation
            unidades = Unidade.objects.filter(
                Q(codigo__icontains=query) |
                Q(unidade__icontains=query)
            ).distinct()[:20]  # Limit to 20 results

        results = []
        for unidade in unidades:
            results.append({
                'id': unidade.id,
                'text': str(unidade)  # Use __str__() method
            })

        return Response({
            'results': results,
            'pagination': {'more': len(results) == 20 if query else len(results) == 10}
        })

class InteressadoSearchAPIView(APIView):
    """
    Search Interessado model by codigo OR concessao fields
    Display only the 'concessao' field value to user
    Returns first 10 records when no query, searches when query provided
    """
    def get(self, request, format=None):
        query = request.GET.get('q', '').strip()

        if not query:
            # Return first 10 records when no search query
            interessados = Interessado.objects.all()[:10]
        else:
            # Search codigo OR concessao fields
            interessados = Interessado.objects.filter(
                Q(codigo__icontains=query) |
                Q(concessao__icontains=query)
            ).distinct()[:20]

        results = []
        for interessado in interessados:
            # Display only the 'concessao' field value
            display_text = interessado.concessao or interessado.codigo or str(interessado)
            results.append({
                'id': interessado.id,
                'text': display_text
            })

        return Response({
            'results': results,
            'pagination': {'more': len(results) == 20 if query else len(results) == 10}
        })

class LocalizacaoSearchAPIView(APIView):
    """
    Search Localizacao model by codigo OR localizacao fields
    Display only the 'localizacao' field value to user
    Returns first 10 records when no query, searches when query provided
    """
    def get(self, request, format=None):
        query = request.GET.get('q', '').strip()

        if not query:
            # Return first 10 records when no search query
            localizacoes = Localizacao.objects.all()[:10]
        else:
            # Search codigo OR localizacao fields
            localizacoes = Localizacao.objects.filter(
                Q(codigo__icontains=query) |
                Q(localizacao__icontains=query)
            ).distinct()[:20]

        results = []
        for localizacao in localizacoes:
            # Display only the 'localizacao' field value
            display_text = localizacao.localizacao or localizacao.codigo or str(localizacao)
            results.append({
                'id': localizacao.id,
                'text': display_text
            })

        return Response({
            'results': results,
            'pagination': {'more': len(results) == 20 if query else len(results) == 10}
        })

class AssuntoSearchAPIView(APIView):
    """
    Search Assunto model by codigo OR doc fields
    Display only the 'doc' field value to user
    Returns first 10 records when no query, searches when query provided
    """
    def get(self, request, format=None):
        query = request.GET.get('q', '').strip()

        if not query:
            # Return first 10 records when no search query
            assuntos = Assunto.objects.all()[:10]
        else:
            # Search codigo OR doc fields
            assuntos = Assunto.objects.filter(
                Q(codigo__icontains=query) |
                Q(doc__icontains=query)
            ).distinct()[:20]

        results = []
        for assunto in assuntos:
            # Display only the 'doc' field value
            display_text = assunto.doc or assunto.codigo or str(assunto)
            results.append({
                'id': assunto.id,
                'text': display_text
            })

        return Response({
            'results': results,
            'pagination': {'more': len(results) == 20 if query else len(results) == 10}
        })

class LocalSearchAPIView(APIView):
    """
    Search Local model by codigo OR local fields
    Display only the 'local' field value to user (note: Local model uses 'local' field, not 'localizacao')
    Returns first 10 records when no query, searches when query provided
    """
    def get(self, request, format=None):
        query = request.GET.get('q', '').strip()

        if not query:
            # Return first 10 records when no search query
            locais = Local.objects.all()[:10]
        else:
            # Search codigo OR local fields
            locais = Local.objects.filter(
                Q(codigo__icontains=query) |
                Q(local__icontains=query)
            ).distinct()[:20]

        results = []
        for local_obj in locais:
            # Display only the 'local' field value
            display_text = local_obj.local or local_obj.codigo or str(local_obj)
            results.append({
                'id': local_obj.id,
                'text': display_text
            })

        return Response({
            'results': results,
            'pagination': {'more': len(results) == 20 if query else len(results) == 10}
        })

class DisciplinaSearchAPIView(APIView):
    """
    Search Disciplina model by codigo OR disciplina fields
    Display only the 'disciplina' field value to user
    Returns first 10 records when no query, searches when query provided
    """
    def get(self, request, format=None):
        query = request.GET.get('q', '').strip()

        if not query:
            # Return first 10 records when no search query
            disciplinas = Disciplina.objects.all()[:10]
        else:
            # Search codigo OR disciplina fields
            disciplinas = Disciplina.objects.filter(
                Q(codigo__icontains=query) |
                Q(disciplina__icontains=query)
            ).distinct()[:20]

        results = []
        for disciplina in disciplinas:
            # Display only the 'disciplina' field value
            display_text = disciplina.disciplina or disciplina.codigo or str(disciplina)
            results.append({
                'id': disciplina.id,
                'text': display_text
            })

        return Response({
            'results': results,
            'pagination': {'more': len(results) == 20 if query else len(results) == 10}
        })

class SeiFormSubmitAPIView(APIView):
    """
    Handle form submission with transaction support
    """

    @transaction.atomic
    def post(self, request, format=None):
        """Main entry point for form submission"""
        try:
            data = request.data

            # Validate requisitante data
            validation_result = self._validate_requisitante_data(data.get('requisitante', {}))
            if validation_result['error']:
                return self._error_response(validation_result['message'])

            # Validate service data
            service_validation = self._validate_service_data(data)
            if service_validation['error']:
                return self._error_response(service_validation['message'])

            # Validate document data
            doc_validation = self._validate_document_data(data)
            if doc_validation['error']:
                return self._error_response(doc_validation['message'])

            # Create requisitante
            requisitante = self._create_requisitante(validation_result['data'], request)

            # Get related objects
            related_objects = self._get_related_objects(data)

            # Generate document code
            doc_cod = self._generate_document_code(
                related_objects,
                service_validation['data'],
                doc_validation['data']
            )

            # Create protocolo
            protocolo = self._create_protocolo(
                requisitante,
                related_objects,
                service_validation['data'],
                doc_validation['data'],
                doc_cod
            )

            return self._success_response(protocolo, doc_cod)

        except Exception as e:
            return self._handle_exception(e)

    def _validate_requisitante_data(self, requisitante_data):
        """Validate and format requisitante data"""
        # Validate nome field
        nome = requisitante_data.get('nome', '').strip()
        if not nome:
            return {'error': True, 'message': 'Nome é obrigatório.'}

        if len(nome) < 3:
            return {'error': True, 'message': 'Nome deve ter pelo menos 3 caracteres.'}

        if len(nome) > 150:
            return {'error': True, 'message': 'Nome deve ter no máximo 150 caracteres.'}

        # Validate email field
        email = requisitante_data.get('email', '').strip().lower()
        if not email:
            return {'error': True, 'message': 'E-mail é obrigatório.'}

        if ' ' in email:
            return {'error': True, 'message': 'E-mail não pode conter espaços.'}

        return {
            'error': False,
            'data': {
                'nome': nome.upper(),
                'email': email
            }
        }

    def _validate_service_data(self, data):
        """Validate service-related data"""
        # Validate servico_tipo field
        servico_tipo = data.get('servico_tipo', '').strip()
        if servico_tipo:  # Only validate if provided (optional field)
            if len(servico_tipo) < 3:
                return {'error': True, 'message': 'Tipo de serviço deve ter pelo menos 3 caracteres.'}

            if len(servico_tipo) > 255:
                return {'error': True, 'message': 'Tipo de serviço deve ter no máximo 255 caracteres.'}

            servico_tipo = servico_tipo.upper()

        # Validate service codigo field
        servico_codigo = data.get('servico_codigo', '').strip()
        if servico_codigo and not servico_codigo.isdigit():
            return {'error': True, 'message': 'Código do serviço deve conter apenas números.'}

        return {
            'error': False,
            'data': {
                'servico_tipo': servico_tipo,
                'servico_codigo': servico_codigo
            }
        }

    def _validate_document_data(self, data):
        """Validate document-related data"""
        doc_revisao = data.get('doc_revisao', '').strip()
        doc_sei_num = data.get('doc_sei_num', '').strip()

        # Validate SEI number is numeric if provided
        if doc_sei_num and not doc_sei_num.isdigit():
            return {'error': True, 'message': 'Número SEI deve conter apenas números.'}

        return {
            'error': False,
            'data': {
                'doc_revisao': doc_revisao,
                'doc_sei_num': doc_sei_num
            }
        }

    def _get_related_objects(self, data):
        """Get and validate related objects from the database"""
        related_objects = {
            'unidade': None,
            'interessado': None,
            'localizacao': None,
            'assunto': None,
            'local': None,
            'disciplina': None
        }

        # Mapping of field names to model classes
        model_mapping = {
            'unidade': Unidade,
            'interessado': Interessado,
            'localizacao': Localizacao,
            'assunto': Assunto,
            'local': Local,
            'disciplina': Disciplina
        }

        for field_name, model_class in model_mapping.items():
            field_id = data.get(f'{field_name}_id')
            if field_id:
                try:
                    related_objects[field_name] = model_class.objects.get(id=field_id)
                except model_class.DoesNotExist:
                    # Silently ignore non-existent objects as they are optional
                    pass

        return related_objects

    def _generate_document_code(self, related_objects, service_data, doc_data):
        """Generate document code by concatenating codigo values"""
        doc_cod_parts = []

        # Add codes from related objects
        for field_name in ['unidade', 'interessado', 'localizacao', 'assunto']:
            obj = related_objects.get(field_name)
            doc_cod_parts.append(obj.codigo if obj and obj.codigo else 'NA')

        # Add service codigo
        doc_cod_parts.append(service_data['servico_codigo'] if service_data['servico_codigo'] else 'NA')

        # Add remaining related object codes
        for field_name in ['local', 'disciplina']:
            obj = related_objects.get(field_name)
            doc_cod_parts.append(obj.codigo if obj and obj.codigo else 'NA')

        # Add document data
        doc_cod_parts.append(doc_data['doc_revisao'] if doc_data['doc_revisao'] else 'NA')
        doc_cod_parts.append(doc_data['doc_sei_num'] if doc_data['doc_sei_num'] else 'NA')

        return '-'.join(doc_cod_parts)

    def _create_requisitante(self, requisitante_data, request):
        """Create Requisitante object"""
        return Requisitante.objects.create(
            nome=requisitante_data['nome'],
            email=requisitante_data['email'],
            ip_address=self._get_client_ip(request)
        )

    def _create_protocolo(self, requisitante, related_objects, service_data, doc_data, doc_cod):
        """Create Protocolo object with secure access password"""
        # Generate secure access password
        access_password = self._generate_access_password()

        return Protocolo.objects.create(
            usuario=requisitante,
            unidade=related_objects['unidade'],
            interessado=related_objects['interessado'],
            localizacao=related_objects['localizacao'],
            assunto=related_objects['assunto'],
            local=related_objects['local'],
            disciplina=related_objects['disciplina'],
            servico_codigo=service_data['servico_codigo'],
            servico_tipo=service_data['servico_tipo'],
            doc_cod=doc_cod,
            doc_revisao=doc_data['doc_revisao'],
            doc_sei_num=doc_data['doc_sei_num'],
            doc_url_cod=access_password  # Store access password in doc_url_cod field
        )

    def _generate_access_password(self):
        """
        Generate a cryptographically secure random password for protocol access.

        Returns:
            str: Secure random password (10 characters, alphanumeric)
        """
        # Define character set: uppercase, lowercase, and digits
        alphabet = string.ascii_letters + string.digits

        # Generate 10-character password using cryptographically secure random
        password = ''.join(secrets.choice(alphabet) for _ in range(10))

        return password

    def _get_client_ip(self, request):
        """Extract client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

    def _error_response(self, message):
        """Create standardized error response"""
        return Response({
            'success': False,
            'message': message
        }, status=status.HTTP_400_BAD_REQUEST)

    def _success_response(self, protocolo, doc_cod):
        """Create standardized success response and trigger email notification"""
        # Trigger async email notification task
        try:
            from .tasks import send_protocolo_email_notification
            send_protocolo_email_notification.delay(str(protocolo.id))
        except Exception as e:
            # Log the error but don't fail the response
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to trigger email notification for protocolo {protocolo.id}: {str(e)}")

        return Response({
            'success': True,
            'message': 'Formulário enviado com sucesso!',
            'protocolo_id': str(protocolo.id),
            'doc_cod': doc_cod
        }, status=status.HTTP_201_CREATED)

    def _handle_exception(self, exception):
        """Handle and log exceptions"""
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error in form submission: {str(exception)}")

        return Response({
            'success': False,
            'message': f'Erro ao processar formulário: {str(exception)}'
        }, status=status.HTTP_400_BAD_REQUEST)


class SeiProtocoloPdfView(TemplateView):
    """
    View to generate and download PDF for a specific protocol.
    """

    def get(self, request, *args, **kwargs):
        protocolo_id = kwargs.get('protocolo_id')

        # Validate protocolo_id
        if not protocolo_id:
            messages.error(request, 'ID do protocolo não fornecido.')
            return redirect('sei:sei_form')

        # Validate user access (can be extended for user-specific access control)
        if not validate_protocolo_access(protocolo_id, request.user):
            messages.error(request, 'Acesso negado ao protocolo.')
            return redirect('sei:sei_form')

        try:
            # Load protocol with all related objects
            protocolo = Protocolo.objects.select_related(
                'usuario', 'unidade', 'interessado', 'localizacao',
                'assunto', 'local', 'disciplina'
            ).get(id=protocolo_id)

        except Protocolo.DoesNotExist:
            messages.error(request, f'Protocolo com ID {protocolo_id} não encontrado.')
            return redirect('sei:sei_form')
        except Exception as e:
            messages.error(request, f'Erro ao carregar protocolo: {str(e)}')
            return redirect('sei:sei_form')

        # Generate PDF filename
        filename = generate_pdf_filename(protocolo)

        # Prepare context for PDF template
        context = get_pdf_context(protocolo)

        # Generate and return PDF
        return render_to_pdf('sei/pdf.html', context, filename)


class SeiProtocoloPdfHtmlView(TemplateView):
    """
    View to render HTML version of the PDF template for preview/debugging.
    """
    template_name = 'sei/pdf.html'

    def dispatch(self, request, *args, **kwargs):
        """
        Override dispatch to validate protocolo_id early and store the protocolo object
        """
        protocolo_id = kwargs.get('protocolo_id')

        if not protocolo_id:
            messages.error(request, 'ID do protocolo não fornecido.')
            return redirect('sei:sei_form')

        # Validate user access (can be extended for user-specific access control)
        if not validate_protocolo_access(protocolo_id, request.user):
            messages.error(request, 'Acesso negado ao protocolo.')
            return redirect('sei:sei_form')

        try:
            self.protocolo = Protocolo.objects.select_related(
                'usuario', 'unidade', 'interessado', 'localizacao',
                'assunto', 'local', 'disciplina'
            ).get(id=protocolo_id)
        except Protocolo.DoesNotExist:
            messages.error(request, f'Protocolo com ID {protocolo_id} não encontrado.')
            return redirect('sei:sei_form')
        except Exception as e:
            messages.error(request, f'Erro ao carregar protocolo: {str(e)}')
            return redirect('sei:sei_form')

        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(get_pdf_context(self.protocolo))
        return context


# -----------------------------------------------------------------------------
# Helper functions for Protocolo listing
# -----------------------------------------------------------------------------

def build_search_query(search_term):
    """
    Build Q object for searching across multiple Protocolo fields.
    """
    if not search_term:
        return Q()

    return (
        Q(doc_cod__icontains=search_term) |
        Q(usuario__nome__icontains=search_term) |
        Q(usuario__email__icontains=search_term) |
        Q(servico_codigo__icontains=search_term) |
        Q(servico_tipo__icontains=search_term) |
        Q(doc_revisao__icontains=search_term) |
        Q(doc_sei_num__icontains=search_term) |
        Q(unidade__unidade__icontains=search_term) |
        Q(interessado__concessao__icontains=search_term) |
        Q(localizacao__localizacao__icontains=search_term) |
        Q(assunto__descricao__icontains=search_term) |
        Q(local__local__icontains=search_term) |
        Q(disciplina__disciplina__icontains=search_term)
    )


def build_filter_query(filters):
    """
    Build Q object for filtering by foreign key fields.
    """
    query = Q()

    filter_mapping = {
        'unidade': 'unidade_id',
        'interessado': 'interessado_id',
        'localizacao': 'localizacao_id',
        'assunto': 'assunto_id',
        'local': 'local_id',
        'disciplina': 'disciplina_id',
    }

    for filter_name, field_name in filter_mapping.items():
        filter_value = filters.get(filter_name)
        if filter_value:
            query &= Q(**{field_name: filter_value})

    # Date range filters
    date_from = filters.get('date_from')
    date_to = filters.get('date_to')

    if date_from:
        query &= Q(created_at__gte=date_from)
    if date_to:
        query &= Q(created_at__lte=date_to)

    return query


def get_protocolo_queryset(search_term=None, filters=None):
    """
    Get filtered and searched Protocolo queryset with optimized database queries.
    """
    queryset = Protocolo.objects.select_related(
        'usuario', 'unidade', 'interessado', 'localizacao',
        'assunto', 'local', 'disciplina'
    ).order_by('-created_at')

    # Apply search
    if search_term:
        queryset = queryset.filter(build_search_query(search_term))

    # Apply filters
    if filters:
        queryset = queryset.filter(build_filter_query(filters))

    return queryset


# -----------------------------------------------------------------------------
# Protocolo listing views
# -----------------------------------------------------------------------------

class ProtocoloListView(TemplateView):
    """
    List view for Protocolo objects with search, filtering, and pagination.
    """
    template_name = 'sei/list.html'
    paginate_by = 10

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get search and filter parameters
        search_term = self.request.GET.get('search', '').strip()
        filters = self.extract_filters()

        # Get filtered queryset
        queryset = get_protocolo_queryset(search_term, filters)

        # Paginate results
        paginator = Paginator(queryset, self.paginate_by)
        page_number = self.request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        context.update({
            'protocolos': page_obj,
            'page_obj': page_obj,
            'paginator': paginator,
            'search_term': search_term,
            'filters': filters,
            'total_count': paginator.count,
        })

        return context

    def extract_filters(self):
        """Extract filter parameters from request."""
        return {
            'unidade': self.request.GET.get('unidade'),
            'interessado': self.request.GET.get('interessado'),
            'localizacao': self.request.GET.get('localizacao'),
            'assunto': self.request.GET.get('assunto'),
            'local': self.request.GET.get('local'),
            'disciplina': self.request.GET.get('disciplina'),
            'date_from': self.request.GET.get('date_from'),
            'date_to': self.request.GET.get('date_to'),
        }


class ProtocoloDetailView(TemplateView):
    """
    Detail view for a specific Protocolo.
    """
    template_name = 'sei/detail.html'

    def dispatch(self, request, *args, **kwargs):
        """Load and validate protocolo access."""
        protocolo_id = kwargs.get('protocolo_id')

        if not protocolo_id:
            messages.error(request, 'ID do protocolo não fornecido.')
            return redirect('sei:protocolo_list')

        if not validate_protocolo_access(protocolo_id, request.user):
            messages.error(request, 'Acesso negado ao protocolo.')
            return redirect('sei:protocolo_list')

        try:
            self.protocolo = get_object_or_404(
                Protocolo.objects.select_related(
                    'usuario', 'unidade', 'interessado', 'localizacao',
                    'assunto', 'local', 'disciplina'
                ),
                id=protocolo_id
            )
        except Exception as e:
            messages.error(request, f'Erro ao carregar protocolo: {str(e)}')
            return redirect('sei:protocolo_list')

        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['protocolo'] = self.protocolo
        return context


class ProtocoloEditView(TemplateView):
    """
    Edit view for a specific Protocolo - allows editing of doc_sei_num field only.
    """
    template_name = 'sei/edit.html'

    def dispatch(self, request, *args, **kwargs):
        """Load and validate protocolo access."""
        protocolo_id = kwargs.get('protocolo_id')

        if not protocolo_id:
            messages.error(request, 'ID do protocolo não fornecido.')
            return redirect('sei:protocolo_list')

        if not validate_protocolo_access(protocolo_id, request.user):
            messages.error(request, 'Acesso negado ao protocolo.')
            return redirect('sei:protocolo_list')

        try:
            self.protocolo = get_object_or_404(
                Protocolo.objects.select_related(
                    'usuario', 'unidade', 'interessado', 'localizacao',
                    'assunto', 'local', 'disciplina'
                ),
                id=protocolo_id
            )
        except Exception as e:
            messages.error(request, f'Erro ao carregar protocolo: {str(e)}')
            return redirect('sei:protocolo_list')

        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['protocolo'] = self.protocolo
        return context

    def post(self, request, *args, **kwargs):
        """Handle form submission for editing doc_sei_num field."""
        try:
            # Get the new doc_sei_num value
            new_doc_sei_num = request.POST.get('doc_sei_num', '').strip()

            # Validate the field (optional field, so empty is allowed)
            if new_doc_sei_num and len(new_doc_sei_num) > 50:  # Reasonable length limit
                messages.error(request, 'Número SEI deve ter no máximo 50 caracteres.')
                return self.get(request, *args, **kwargs)

            # Update only the doc_sei_num field
            old_value = self.protocolo.doc_sei_num
            self.protocolo.doc_sei_num = new_doc_sei_num
            self.protocolo.save(update_fields=['doc_sei_num', 'updated_at'])

            # Success message with details
            if old_value != new_doc_sei_num:
                if new_doc_sei_num:
                    messages.success(request, f'Número SEI atualizado com sucesso para: {new_doc_sei_num}')
                else:
                    messages.success(request, 'Número SEI removido com sucesso.')
            else:
                messages.info(request, 'Nenhuma alteração foi feita no Número SEI.')

            return redirect('sei:protocolo_detail', protocolo_id=self.protocolo.id)

        except Exception as e:
            messages.error(request, f'Erro ao atualizar protocolo: {str(e)}')
            return self.get(request, *args, **kwargs)


class ProtocoloDeleteView(TemplateView):
    """
    Delete confirmation view for a specific Protocolo.
    """
    template_name = 'sei/delete.html'

    def dispatch(self, request, *args, **kwargs):
        """Load and validate protocolo access."""
        protocolo_id = kwargs.get('protocolo_id')

        if not protocolo_id:
            messages.error(request, 'ID do protocolo não fornecido.')
            return redirect('sei:protocolo_list')

        if not validate_protocolo_access(protocolo_id, request.user):
            messages.error(request, 'Acesso negado ao protocolo.')
            return redirect('sei:protocolo_list')

        try:
            self.protocolo = get_object_or_404(Protocolo, id=protocolo_id)
        except Exception as e:
            messages.error(request, f'Erro ao carregar protocolo: {str(e)}')
            return redirect('sei:protocolo_list')

        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['protocolo'] = self.protocolo
        return context

    def post(self, request, *args, **kwargs):
        """Handle protocolo deletion with confirmation validation."""
        confirmation = request.POST.get('confirmation', '').strip()

        # Validate confirmation code
        if confirmation != self.protocolo.doc_cod:
            messages.error(request, 'Código de confirmação incorreto. A exclusão foi cancelada.')
            return self.get(request, *args, **kwargs)

        try:
            doc_cod = self.protocolo.doc_cod
            self.protocolo.delete()
            messages.success(request, f'Protocolo {doc_cod} excluído com sucesso!')
        except Exception as e:
            messages.error(request, f'Erro ao excluir protocolo: {str(e)}')

        return redirect('sei:protocolo_list')


# -----------------------------------------------------------------------------
# Secure Protocol Access Views
# -----------------------------------------------------------------------------

def _parse_session_timestamp(timestamp_str):
    """
    Parse session timestamp string to timezone-aware datetime object.

    Args:
        timestamp_str (str): ISO format timestamp string

    Returns:
        datetime: Timezone-aware datetime object or None if parsing fails
    """
    if not timestamp_str or not isinstance(timestamp_str, str):
        logger.error(f"Invalid timestamp input: {timestamp_str} (type: {type(timestamp_str)})")
        return None

    try:
        logger.info(f"Parsing timestamp: {timestamp_str}")

        # Handle various ISO format variations
        clean_timestamp = timestamp_str.replace('Z', '+00:00')
        logger.info(f"Cleaned timestamp: {clean_timestamp}")

        # Parse the datetime
        dt = datetime.fromisoformat(clean_timestamp)
        logger.info(f"Parsed datetime: {dt} (tzinfo: {dt.tzinfo})")

        # Ensure timezone awareness
        if dt.tzinfo is None:
            logger.info("Making datetime timezone-aware")
            dt = timezone.make_aware(dt)

        logger.info(f"Final datetime: {dt} (tzinfo: {dt.tzinfo})")
        return dt

    except (ValueError, TypeError, AttributeError) as e:
        logger.error(f"Error parsing timestamp '{timestamp_str}': {str(e)}")
        return None

@method_decorator(csrf_protect, name='dispatch')
class ProtocoloAccessView(TemplateView):
    """
    Secure access form for protocol editing.
    Requires email and access password validation.
    """
    template_name = 'sei/access_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_errors'] = self.request.session.pop('form_errors', {})
        return context

    def post(self, request, *args, **kwargs):
        """Handle access form submission with security measures"""
        email = request.POST.get('email', '').strip().lower()
        password = request.POST.get('password', '').strip()

        # Rate limiting check
        if self._is_rate_limited(request, email):
            messages.error(request, 'Muitas tentativas de acesso. Tente novamente em alguns minutos.')
            return self.get(request, *args, **kwargs)

        # Validate credentials
        protocolo = self._validate_access_credentials(email, password)

        if protocolo:
            # Log successful access
            logger.info(f"Successful protocol access for email {email}, protocol {protocolo.id}")

            # Store protocol ID in session for secure access
            request.session['authenticated_protocolo_id'] = str(protocolo.id)

            # Store timestamp in a consistent timezone-aware format
            current_time = timezone.now()
            timestamp_str = current_time.isoformat()
            request.session['access_timestamp'] = timestamp_str

            # Force session save
            request.session.save()

            # Log timestamp for debugging
            logger.info(f"Stored access timestamp: {timestamp_str}")
            logger.info(f"Current time details: {current_time} (tzinfo: {current_time.tzinfo})")

            # Test parsing immediately to catch issues early
            test_parse = _parse_session_timestamp(timestamp_str)
            logger.info(f"Test parse result: {test_parse} (tzinfo: {test_parse.tzinfo if test_parse else None})")

            # Clear rate limiting
            self._clear_rate_limit(request, email)

            # Log session data for debugging
            logger.info(f"Session data set: protocolo_id={request.session.get('authenticated_protocolo_id')}, "
                       f"timestamp={request.session.get('access_timestamp')}")

            # Log redirect attempt
            logger.info(f"Redirecting to secure edit for protocol {protocolo.id}")

            return redirect('sei:protocolo_secure_edit', protocolo_id=protocolo.id)
        else:
            # Log failed access attempt
            logger.warning(f"Failed protocol access attempt for email {email}")

            # Increment rate limiting
            self._increment_rate_limit(request, email)

            messages.error(request, 'E-mail ou senha de acesso inválidos.')
            return self.get(request, *args, **kwargs)

    def _validate_access_credentials(self, email, password):
        """
        Validate email and password against protocol records.

        Args:
            email (str): User email address
            password (str): Access password

        Returns:
            Protocolo: Protocol instance if valid, None otherwise
        """
        if not email or not password:
            return None

        try:
            # Find protocol with matching email and password
            protocolo = Protocolo.objects.select_related('usuario').get(
                usuario__email=email,
                doc_url_cod=password
            )

            # Check if password is still valid (7 days from creation)
            if self._is_password_expired(protocolo):
                logger.info(f"Expired password used for protocol {protocolo.id}")
                return None

            return protocolo

        except Protocolo.DoesNotExist:
            return None
        except Exception as e:
            logger.error(f"Error validating access credentials: {str(e)}")
            return None

    def _is_password_expired(self, protocolo):
        """Check if access password has expired (7 days)"""
        if not protocolo.created_at:
            return True

        expiry_date = protocolo.created_at + timedelta(days=7)
        return timezone.now() > expiry_date

    def _is_rate_limited(self, request, email):
        """Check if user is rate limited"""
        cache_key = f"access_attempts_{email}_{request.META.get('REMOTE_ADDR', 'unknown')}"
        attempts = cache.get(cache_key, 0)
        return attempts >= 5  # Max 5 attempts

    def _increment_rate_limit(self, request, email):
        """Increment rate limiting counter"""
        cache_key = f"access_attempts_{email}_{request.META.get('REMOTE_ADDR', 'unknown')}"
        attempts = cache.get(cache_key, 0)
        cache.set(cache_key, attempts + 1, 300)  # 5 minutes timeout

    def _clear_rate_limit(self, request, email):
        """Clear rate limiting counter on successful access"""
        cache_key = f"access_attempts_{email}_{request.META.get('REMOTE_ADDR', 'unknown')}"
        cache.delete(cache_key)


@method_decorator(csrf_protect, name='dispatch')
class ProtocoloSecureEditView(TemplateView):
    """
    Secure edit view for protocols accessed via password authentication.
    Only allows editing of doc_sei_num field.
    """
    template_name = 'sei/secure_edit.html'

    def dispatch(self, request, *args, **kwargs):
        """Validate secure access before allowing edit"""
        protocolo_id = kwargs.get('protocolo_id')

        # Log incoming request for debugging
        logger.info(f"Secure edit dispatch: protocolo_id={protocolo_id}")

        # Check if user has authenticated access to this protocol
        authenticated_id = request.session.get('authenticated_protocolo_id')
        logger.info(f"Session authenticated_id={authenticated_id}, requested_id={protocolo_id}")
        logger.info(f"Types: authenticated_id={type(authenticated_id)}, protocolo_id={type(protocolo_id)}")

        # Convert both to strings for comparison since UUID objects and strings might not match
        if not authenticated_id or str(authenticated_id) != str(protocolo_id):
            logger.warning(f"Access denied: authenticated_id={authenticated_id}, protocolo_id={protocolo_id}")
            messages.error(request, 'Acesso não autorizado. Faça login novamente.')
            return redirect('sei:protocolo_access')

        # Check session timeout (30 minutes)
        access_timestamp = request.session.get('access_timestamp')
        if access_timestamp:
            access_time = _parse_session_timestamp(access_timestamp)

            if access_time is None:
                logger.error(f"Failed to parse access timestamp: {access_timestamp}")
                request.session.pop('authenticated_protocolo_id', None)
                request.session.pop('access_timestamp', None)
                messages.error(request, 'Sessão inválida. Faça login novamente.')
                return redirect('sei:protocolo_access')

            # Calculate time difference with proper timezone handling
            current_time = timezone.now()

            # Double-check timezone awareness before subtraction
            if access_time.tzinfo is None:
                logger.warning("Access time is still timezone-naive, making it aware")
                access_time = timezone.make_aware(access_time)

            if current_time.tzinfo is None:
                logger.warning("Current time is timezone-naive, this should not happen")
                current_time = timezone.make_aware(current_time)

            logger.info(f"Before calculation: current={current_time} (tz: {current_time.tzinfo}), "
                       f"access={access_time} (tz: {access_time.tzinfo})")

            try:
                time_diff = current_time - access_time
                logger.info(f"Session timeout check: diff={time_diff}")
            except TypeError as e:
                logger.error(f"Timezone calculation error: {str(e)}")
                # Force session clear on timezone errors
                request.session.pop('authenticated_protocolo_id', None)
                request.session.pop('access_timestamp', None)
                messages.error(request, 'Erro de sessão. Faça login novamente.')
                return redirect('sei:protocolo_access')

            if time_diff > timedelta(minutes=30):
                logger.info(f"Session expired for protocol {protocolo_id} (diff: {time_diff})")
                request.session.pop('authenticated_protocolo_id', None)
                request.session.pop('access_timestamp', None)
                messages.error(request, 'Sessão expirada. Faça login novamente.')
                return redirect('sei:protocolo_access')

        # Load protocol
        try:
            self.protocolo = Protocolo.objects.select_related(
                'usuario', 'unidade', 'interessado', 'localizacao',
                'assunto', 'local', 'disciplina'
            ).get(id=protocolo_id)

            # Add success message for successful authentication
            if not messages.get_messages(request):  # Only if no other messages
                messages.success(request, f'Acesso autorizado para o protocolo {self.protocolo.doc_cod}')

        except Protocolo.DoesNotExist:
            messages.error(request, 'Protocolo não encontrado.')
            return redirect('sei:protocolo_access')
        except Exception as e:
            logger.error(f"Error loading protocol for secure edit: {str(e)}")
            messages.error(request, 'Erro ao carregar protocolo.')
            return redirect('sei:protocolo_access')

        logger.info(f"Successfully loaded protocol {protocolo_id} for secure edit")
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['protocolo'] = self.protocolo
        return context

    def post(self, request, *args, **kwargs):
        """Handle secure edit form submission with transaction support"""
        # Get the new doc_sei_num value
        new_doc_sei_num = request.POST.get('doc_sei_num', '').strip()

        # Validate the field (validation outside transaction)
        if new_doc_sei_num and len(new_doc_sei_num) > 50:
            messages.error(request, 'Número SEI deve ter no máximo 50 caracteres.')
            return self.get(request, *args, **kwargs)

        # Store old value for comparison
        old_value = self.protocolo.doc_sei_num

        # Check if there's actually a change
        if old_value == new_doc_sei_num:
            messages.info(request, 'Nenhuma alteração foi feita no Número SEI.')
            return self.get(request, *args, **kwargs)

        try:
            # Perform database operations within transaction
            with transaction.atomic():
                # Update only the doc_sei_num field
                self.protocolo.doc_sei_num = new_doc_sei_num
                self.protocolo.save(update_fields=['doc_sei_num', 'updated_at'])

                # Log the edit within transaction
                logger.info(f"Secure edit completed for protocol {self.protocolo.id}: "
                           f"doc_sei_num changed from '{old_value}' to '{new_doc_sei_num}'")

            # After successful transaction, trigger async operations
            try:
                from .tasks import send_protocolo_update_notification
                send_protocolo_update_notification.delay(
                    str(self.protocolo.id),
                    old_value,
                    new_doc_sei_num
                )
                logger.info(f"Update notification email task queued for protocol {self.protocolo.id}")
            except Exception as e:
                # Log the error but don't fail the edit since DB update succeeded
                logger.error(f"Failed to queue update notification email for protocol {self.protocolo.id}: {str(e)}")

            # Success message with details
            if new_doc_sei_num:
                messages.success(request, f'Número SEI atualizado com sucesso para: {new_doc_sei_num}')
            else:
                messages.success(request, 'Número SEI removido com sucesso.')

            # Clear session after successful edit
            request.session.pop('authenticated_protocolo_id', None)
            request.session.pop('access_timestamp', None)

            return redirect('sei:protocolo_secure_success', protocolo_id=self.protocolo.id)

        except Exception as e:
            # Transaction will be automatically rolled back
            logger.error(f"Error in secure edit for protocol {self.protocolo.id}: {str(e)}")
            messages.error(request, f'Erro ao atualizar protocolo: {str(e)}')
            return self.get(request, *args, **kwargs)


class ProtocoloSecureSuccessView(TemplateView):
    """
    Success view after secure protocol edit.
    Shows confirmation and read-only protocol information.
    """
    template_name = 'sei/secure_success.html'

    def dispatch(self, request, *args, **kwargs):
        """Load protocol for display"""
        protocolo_id = kwargs.get('protocolo_id')

        try:
            self.protocolo = Protocolo.objects.select_related(
                'usuario', 'unidade', 'interessado', 'localizacao',
                'assunto', 'local', 'disciplina'
            ).get(id=protocolo_id)
        except Protocolo.DoesNotExist:
            messages.error(request, 'Protocolo não encontrado.')
            return redirect('sei:protocolo_access')
        except Exception as e:
            logger.error(f"Error loading protocol for success view: {str(e)}")
            messages.error(request, 'Erro ao carregar protocolo.')
            return redirect('sei:protocolo_access')

        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['protocolo'] = self.protocolo
        return context
