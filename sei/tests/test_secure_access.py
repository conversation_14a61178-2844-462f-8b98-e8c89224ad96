"""
Test cases for SEI secure protocol access functionality.
"""

from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from django.core.cache import cache
from datetime import timedelta
import json
from ..models import Protocolo, Requisitante


class SecureAccessTest(TestCase):
    """Test cases for secure protocol access system"""
    
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.requisitante = Requisitante.objects.create(
            nome='JOÃO SILVA SANTOS',
            email='<EMAIL>',
            ip_address='*************'
        )
        
        # Create a protocol with access password
        self.protocolo = Protocolo.objects.create(
            usuario=self.requisitante,
            doc_cod='TEST-SECURE-001-2024',
            servico_codigo='123',
            servico_tipo='ANÁLISE TÉCNICA',
            doc_url_cod='ABC123XYZ0',  # Access password
            doc_sei_num='12345'
        )
        
        self.access_url = reverse('sei:protocolo_access')
        self.secure_edit_url = reverse('sei:protocolo_secure_edit', kwargs={'protocolo_id': self.protocolo.id})
    
    def tearDown(self):
        """Clean up cache after each test"""
        cache.clear()
    
    def test_access_form_loads(self):
        """Test that the access form loads correctly"""
        response = self.client.get(self.access_url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Acesso Seguro ao Protocolo')
        self.assertContains(response, 'E-mail do Requisitante')
        self.assertContains(response, 'Senha de Acesso')
    
    def test_successful_access(self):
        """Test successful access with valid credentials"""
        response = self.client.post(self.access_url, {
            'email': '<EMAIL>',
            'password': 'ABC123XYZ0'
        })
        
        # Should redirect to secure edit page
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, self.secure_edit_url)
        
        # Check session data
        self.assertEqual(self.client.session['authenticated_protocolo_id'], str(self.protocolo.id))
        self.assertIn('access_timestamp', self.client.session)
    
    def test_invalid_email(self):
        """Test access with invalid email"""
        response = self.client.post(self.access_url, {
            'email': '<EMAIL>',
            'password': 'ABC123XYZ0'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'E-mail ou senha de acesso inválidos')
    
    def test_invalid_password(self):
        """Test access with invalid password"""
        response = self.client.post(self.access_url, {
            'email': '<EMAIL>',
            'password': 'WRONGPASS'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'E-mail ou senha de acesso inválidos')
    
    def test_expired_password(self):
        """Test access with expired password"""
        # Create protocol with old creation date
        old_protocolo = Protocolo.objects.create(
            usuario=self.requisitante,
            doc_cod='OLD-PROTOCOL-2024',
            doc_url_cod='OLDPASS123',
            created_at=timezone.now() - timedelta(days=8)  # 8 days old
        )
        
        response = self.client.post(self.access_url, {
            'email': '<EMAIL>',
            'password': 'OLDPASS123'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'E-mail ou senha de acesso inválidos')
    
    def test_rate_limiting(self):
        """Test rate limiting after multiple failed attempts"""
        # Make 5 failed attempts
        for i in range(5):
            response = self.client.post(self.access_url, {
                'email': '<EMAIL>',
                'password': 'WRONGPASS'
            })
            self.assertEqual(response.status_code, 200)
        
        # 6th attempt should be rate limited
        response = self.client.post(self.access_url, {
            'email': '<EMAIL>',
            'password': 'WRONGPASS'
        })
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Muitas tentativas de acesso')
    
    def test_secure_edit_without_authentication(self):
        """Test accessing secure edit without authentication"""
        response = self.client.get(self.secure_edit_url)
        
        # Should redirect to access form
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, self.access_url)
    
    def test_secure_edit_with_authentication(self):
        """Test accessing secure edit with valid authentication"""
        # First authenticate
        self.client.post(self.access_url, {
            'email': '<EMAIL>',
            'password': 'ABC123XYZ0'
        })
        
        # Then access secure edit
        response = self.client.get(self.secure_edit_url)
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Edição Segura do Protocolo')
        self.assertContains(response, 'TEST-SECURE-001-2024')
        self.assertContains(response, 'Número SEI')
    
    def test_secure_edit_form_submission(self):
        """Test submitting the secure edit form"""
        # First authenticate
        self.client.post(self.access_url, {
            'email': '<EMAIL>',
            'password': 'ABC123XYZ0'
        })
        
        # Submit edit form
        response = self.client.post(self.secure_edit_url, {
            'doc_sei_num': '67890-NEW'
        })
        
        # Should redirect to success page
        success_url = reverse('sei:protocolo_secure_success', kwargs={'protocolo_id': self.protocolo.id})
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, success_url)
        
        # Check that the field was updated
        self.protocolo.refresh_from_db()
        self.assertEqual(self.protocolo.doc_sei_num, '67890-NEW')
        
        # Check that session was cleared
        self.assertNotIn('authenticated_protocolo_id', self.client.session)
    
    def test_session_timeout(self):
        """Test session timeout functionality"""
        # Authenticate and manually set old timestamp
        self.client.post(self.access_url, {
            'email': '<EMAIL>',
            'password': 'ABC123XYZ0'
        })
        
        # Manually set old timestamp (31 minutes ago)
        session = self.client.session
        session['access_timestamp'] = (timezone.now() - timedelta(minutes=31)).isoformat()
        session.save()
        
        # Try to access secure edit
        response = self.client.get(self.secure_edit_url)
        
        # Should redirect to access form due to timeout
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, self.access_url)
    
    def test_password_generation_in_form_submission(self):
        """Test that password is generated during form submission"""
        submit_url = reverse('sei:form_submit')
        
        form_data = {
            'requisitante': {
                'nome': 'Test User',
                'email': '<EMAIL>'
            },
            'servico_codigo': '999',
            'servico_tipo': 'Test Service'
        }
        
        response = self.client.post(
            submit_url,
            data=json.dumps(form_data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 201)
        result = response.json()
        
        # Get the created protocol
        protocolo = Protocolo.objects.get(id=result['protocolo_id'])
        
        # Verify password was generated
        self.assertIsNotNone(protocolo.doc_url_cod)
        self.assertEqual(len(protocolo.doc_url_cod), 10)  # 10 character password
        self.assertTrue(protocolo.doc_url_cod.isalnum())  # Alphanumeric only


class SecureAccessSecurityTest(TestCase):
    """Test cases for security aspects of the access system"""
    
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        self.requisitante = Requisitante.objects.create(
            nome='SECURITY TEST USER',
            email='<EMAIL>',
            ip_address='*************'
        )
        
        self.protocolo = Protocolo.objects.create(
            usuario=self.requisitante,
            doc_cod='SECURITY-TEST-2024',
            doc_url_cod='SECUREPASS',
        )
        
        self.access_url = reverse('sei:protocolo_access')
    
    def tearDown(self):
        """Clean up cache after each test"""
        cache.clear()
    
    def test_csrf_protection(self):
        """Test that CSRF protection is enabled"""
        response = self.client.get(self.access_url)
        self.assertContains(response, 'csrfmiddlewaretoken')
    
    def test_password_not_exposed_in_error_messages(self):
        """Test that passwords are not exposed in error messages"""
        response = self.client.post(self.access_url, {
            'email': '<EMAIL>',
            'password': 'WRONGPASS'
        })
        
        # Error message should not contain the actual password
        self.assertNotContains(response, 'SECUREPASS')
        self.assertNotContains(response, 'WRONGPASS')
    
    def test_email_case_insensitive(self):
        """Test that email matching is case insensitive"""
        response = self.client.post(self.access_url, {
            'email': '<EMAIL>',  # Uppercase
            'password': 'SECUREPASS'
        })
        
        # Should still work with uppercase email
        self.assertEqual(response.status_code, 302)


if __name__ == '__main__':
    print("Testing SEI secure access functionality...")
    print("Run with: python manage.py test sei.test_secure_access")
