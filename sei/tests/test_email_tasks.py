"""
Test cases for SEI email notification tasks.
"""

from django.test import TestCase
from django.core import mail
from django.template.loader import render_to_string
from unittest.mock import patch, MagicMock
from ..models import Protocolo, Requisitante
from ..tasks import send_protocolo_email_notification, _prepare_email_context, _generate_pdf_content


class EmailNotificationTaskTest(TestCase):
    """Test cases for email notification Celery task"""
    
    def setUp(self):
        """Set up test data"""
        self.requisitante = Requisitante.objects.create(
            nome='JOÃO SILVA SANTOS',
            email='<EMAIL>',
            ip_address='*************'
        )
        
        self.protocolo = Protocolo.objects.create(
            usuario=self.requisitante,
            doc_cod='TEST-EMAIL-001-2024',
            servico_codigo='123',
            servico_tipo='ANÁLISE TÉCNICA',
            doc_revisao='REV01',
            doc_sei_num='12345'
        )
    
    def test_prepare_email_context(self):
        """Test email context preparation"""
        context = _prepare_email_context(self.protocolo)
        
        # Verify basic context data
        self.assertEqual(context['protocolo'], self.protocolo)
        self.assertEqual(context['doc_cod'], 'TEST-EMAIL-001-2024')
        self.assertEqual(context['usuario_nome'], 'JOÃO SILVA SANTOS')
        self.assertEqual(context['usuario_email'], '<EMAIL>')
        
        # Verify service data
        self.assertEqual(context['servico']['codigo'], '123')
        self.assertEqual(context['servico']['tipo'], 'ANÁLISE TÉCNICA')
        
        # Verify additional info
        self.assertEqual(context['additional_info']['doc_revisao'], 'REV01')
        self.assertEqual(context['additional_info']['doc_sei_num'], '12345')
    
    def test_prepare_email_context_with_null_values(self):
        """Test email context preparation with null/empty values"""
        # Create protocolo with minimal data
        minimal_protocolo = Protocolo.objects.create(
            usuario=self.requisitante,
            doc_cod='MINIMAL-TEST'
        )
        
        context = _prepare_email_context(minimal_protocolo)
        
        # Verify default values are used
        self.assertEqual(context['unidade']['unidade'], 'Não informado')
        self.assertEqual(context['interessado']['codigo'], 'Não informado')
        self.assertEqual(context['servico']['codigo'], 'Não informado')
        self.assertEqual(context['additional_info']['doc_revisao'], 'Não aplicável')
    
    @patch('sei.tasks.pisa.CreatePDF')
    def test_generate_pdf_content_success(self, mock_create_pdf):
        """Test successful PDF generation"""
        # Mock successful PDF creation
        mock_create_pdf.return_value = MagicMock(err=False)
        
        pdf_content = _generate_pdf_content(self.protocolo)
        
        # Verify PDF generation was attempted
        mock_create_pdf.assert_called_once()
        self.assertIsNotNone(pdf_content)
    
    @patch('sei.tasks.pisa.CreatePDF')
    def test_generate_pdf_content_failure(self, mock_create_pdf):
        """Test PDF generation failure"""
        # Mock PDF creation failure
        mock_create_pdf.return_value = MagicMock(err=True)
        
        pdf_content = _generate_pdf_content(self.protocolo)
        
        # Verify None is returned on failure
        self.assertIsNone(pdf_content)
    
    @patch('sei.tasks._send_email_with_pdf')
    @patch('sei.tasks._generate_pdf_content')
    def test_email_task_success(self, mock_generate_pdf, mock_send_email):
        """Test successful email task execution"""
        # Mock successful PDF generation and email sending
        mock_generate_pdf.return_value = b'fake_pdf_content'
        mock_send_email.return_value = {'success': True, 'email': '<EMAIL>'}
        
        # Execute the task
        result = send_protocolo_email_notification(str(self.protocolo.id))
        
        # Verify success
        self.assertTrue(result['success'])
        self.assertEqual(result['protocolo_id'], str(self.protocolo.id))
        self.assertEqual(result['email'], '<EMAIL>')
    
    @patch('sei.tasks._generate_pdf_content')
    def test_email_task_pdf_generation_failure(self, mock_generate_pdf):
        """Test email task when PDF generation fails"""
        # Mock PDF generation failure
        mock_generate_pdf.return_value = None
        
        # Execute the task
        result = send_protocolo_email_notification(str(self.protocolo.id))
        
        # Verify failure
        self.assertFalse(result['success'])
        self.assertEqual(result['error'], 'PDF generation failed')
    
    def test_email_task_nonexistent_protocolo(self):
        """Test email task with non-existent protocolo ID"""
        fake_id = '00000000-0000-0000-0000-000000000000'
        
        # Execute the task
        result = send_protocolo_email_notification(fake_id)
        
        # Verify failure
        self.assertFalse(result['success'])
        self.assertEqual(result['error'], 'Protocolo not found')
    
    def test_email_task_no_email_address(self):
        """Test email task when requisitante has no email"""
        # Create protocolo without email
        protocolo_no_email = Protocolo.objects.create(
            usuario=None,
            doc_cod='NO-EMAIL-TEST'
        )
        
        # Execute the task
        result = send_protocolo_email_notification(str(protocolo_no_email.id))
        
        # Verify failure
        self.assertFalse(result['success'])
        self.assertEqual(result['error'], 'No valid email address')
    
    def test_email_templates_render_correctly(self):
        """Test that email templates render without errors"""
        context = _prepare_email_context(self.protocolo)
        
        # Test HTML template
        try:
            html_content = render_to_string('sei/email/protocolo_notification.html', context)
            self.assertIn('TEST-EMAIL-001-2024', html_content)
            self.assertIn('JOÃO SILVA SANTOS', html_content)
        except Exception as e:
            self.fail(f"HTML template rendering failed: {str(e)}")
        
        # Test text template
        try:
            text_content = render_to_string('sei/email/protocolo_notification.txt', context)
            self.assertIn('TEST-EMAIL-001-2024', text_content)
            self.assertIn('JOÃO SILVA SANTOS', text_content)
        except Exception as e:
            self.fail(f"Text template rendering failed: {str(e)}")


class EmailIntegrationTest(TestCase):
    """Integration tests for email functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.requisitante = Requisitante.objects.create(
            nome='MARIA SANTOS SILVA',
            email='<EMAIL>',
            ip_address='*************'
        )
    
    @patch('sei.tasks.send_protocolo_email_notification.delay')
    def test_email_task_triggered_on_form_submission(self, mock_task):
        """Test that email task is triggered when form is submitted successfully"""
        from django.test import Client
        from django.urls import reverse
        import json
        
        client = Client()
        submit_url = reverse('sei:form_submit')
        
        form_data = {
            'requisitante': {
                'nome': 'Test User',
                'email': '<EMAIL>'
            },
            'servico_codigo': '999',
            'servico_tipo': 'Test Service'
        }
        
        response = client.post(
            submit_url,
            data=json.dumps(form_data),
            content_type='application/json'
        )
        
        # Verify successful response
        self.assertEqual(response.status_code, 201)
        
        # Verify email task was triggered
        mock_task.assert_called_once()


if __name__ == '__main__':
    print("Testing SEI email notification functionality...")
    print("Run with: python manage.py test sei.test_email_tasks")
