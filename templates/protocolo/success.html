{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8">
    <style>
        /* Existing global styles */
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #fff;
            color: #333;
            position: relative;
            min-height: 100vh;
        }
        
        .header {
            text-align: center;
            margin-bottom: 0px;
        }
        
        table {
            width: 60%;
            margin: 20px auto;
            border-collapse: collapse;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }
        
        th, td {
            padding: 10px;
            text-align: left;
            font-size: 12px;
            color: #333;
            border: 1px solid #ddd;
        }
        
        th {
            background-color: #000;
            color: #fff;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        td {
            background-color: #f9f9f9;
            border-bottom: 1px solid #ddd;
        }
        
        tr:last-child td {
            border-bottom: none;
        }
        
        .content-table {
            margin-top: 20px;
            margin-bottom: 120px;
        }
        
        .content-table tr:hover {
            background-color: #f1f1f1;
        }
        
        tr:nth-child(even) td {
            background-color: #fafafa;
        }
        
        tr:nth-child(odd) td {
            background-color: #fff;
        }
        
        .header p {
            font-size: 16px;
            color: #333;
            margin: 0;
            padding: 0;
        }
        
        .header img {
            margin-bottom: 0;
        }
        
        /* Download links styling */
        #links {
            position: fixed;
            left: 50%;
            bottom: 50px;
            transform: translateX(-50%);
            text-align: center;
            background-color: #da2908;
            padding: 10px 20px;
            border-radius: 25px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        }
        
        #links a {
            color: white;
            text-decoration: none;
            font-weight: bold;
            margin: 0 15px;
            font-size: 12px;
            transition: color 0.3s ease;
        }
        
        #links a:hover {
            color: #f1f1f1;
        }
        
        #btn__copia-protocolo {
            text-align: center;
            background-color: #da2908;
            padding: 5px 10px;
            border-radius: 25px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            color: white;
            text-decoration: none;
            font-weight: bold;
            margin: 15px 5px;
            font-size: 12px;
            transition: color 0.3s ease;
        }
        
        #btn__copia-protocolo:hover {
            color: #f1f1f1;
            cursor: pointer;
        }
        
        /* New styles for the instructions section */
        .instruction-container {
            width: 60%;
            margin: 0px auto 20px auto;
            padding: 0px 20px 20px 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }
        
        .instruction-header {
            color: #da2908;
            font-size: 18px;
            margin-top: 0;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .instruction-protocolo {
            text-align: center;
            border: 1px solid #e9e9e9;
            padding: 16px 0;
        }
        
        .instruction-paragraph {
            color: #333;
            font-size: 14px;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .instruction-list {
            list-style-type: decimal;
            padding-left: 20px;
            margin-bottom: 20px;
        }
        
        .instruction-list li {
            color: #333;
            font-size: 14px;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .instruction-alert {
            background-color: #ffebee;
            border-left: 4px solid #da2908;
            padding: 15px;
            margin-top: 20px;
        }
        
        .instruction-alert p {
            color: #000;
            font-size: 14px;
            margin: 0;
            line-height: 1.5;
        }
        
        /* New style for the SEI link */
        .sei-link {
            color: #da2908;
            text-decoration: none;
            font-weight: bold;
        }
        
        /* New styles for instruction images */
        .instruction-img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 10px auto;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>

    <div class="header">
        <img src="{% static 'images/home/<USER>' %}" alt="Logo" />
    </div>

    <!-- Instructions Section -->
    <div class="instruction-container">
        <h2 class="instruction-header">
            ✓ Protocolo registrado com sucesso!
        </h2>

        <p class="instruction-protocolo">
            PROTOCOLO:<strong><span id="text__protocolo">{{ protocolo.protocolo }}</span></strong>
            <span class="btn_copy" id="btn__copia-protocolo" aria-label="clique p/ copiar">copiar nro.</span>
        </p>
        
        <p class="instruction-paragraph">
            Agora, para prosseguir com o encaminhamento de sua solicitação no SEI!, siga os passos abaixo:
        </p>

        <ol class="instruction-list">
            <li>
                Acesse o portal SEI Usuário Externo pelo link, colocando seus dados de login e senha:<br>
                <a href="https://portal.sei.sp.gov.br/sei/usuario_externo" class="sei-link" target="_blank">
                    https://portal.sei.sp.gov.br/sei/usuario_externo
                </a>
            </li>
            
            <li>
                Inicie o peticionamento de processo novo, escolhendo o tipo <strong>"ARTESP: Atendimento às Solicitações"</strong>
                <br>
                <img src="{% static 'images/protocolo/step2-min.png' %}" alt="Step 2" class="instruction-img">
            </li>
            
            <li>
                No campo "Especificação", coloque exatamente o seu <strong>código de protocolo</strong> gerado neste formulário:<br>
                <strong>{{ protocolo.protocolo }}</strong>
                <br>
                <img src="{% static 'images/protocolo/step3-min.png' %}" alt="Step 3" class="instruction-img">
            </li>
            
            <li>
                Na seção <strong>"Documento Principal"</strong>, faça o upload do PDF gerado no preenchimento deste protocolo, ao passo que os demais documentos que irão compor a solicitação deverão ser anexados na seção <strong>"Documentos Complementares"</strong>
                <br>
                <img src="{% static 'images/protocolo/step4-min.png' %}" alt="Step 4" class="instruction-img">
            </li>
        </ol>

        <div class="instruction-alert">
            <p>
                <strong style="color: #da2908;">ATENÇÃO:</strong> O TEXTO A SER COLOCADO NO CAMPO <strong>"ESPECIFICAÇÃO"</strong> DEVE SER EXATAMENTE IGUAL AO CÓDIGO DE PROTOCOLO GERADO NESTE FORMULÁRIO, NÃO SENDO PERMITIDAS PERSONALIZAÇÕES DESTE CAMPO.
            </p>
        </div>
    </div>
    <!-- End Instructions Section -->

    <table class="content-table">
        <tr>
            <th>NOME</th>
            <td>{{ protocolo.user_name|default:"-" }}</td>
        </tr>
        <tr>
            <th>EMAIL</th>
            <td>{{ protocolo.user_email|default:"-" }}</td>
        </tr>
        <tr>
            <th>PROTOCOLO</th>
            <td>{{ protocolo.protocolo|default:"-" }}</td>
        </tr>
        <tr>
            <th>ASSUNTO</th>
            <td>{{ protocolo.assunto.description|default:"-" }}</td>
        </tr>
        <tr>
            <th>DESTINATÁRIO</th>
            <td>{{ protocolo.destinatario.description|default:"-" }}</td>
        </tr>
        <tr>
            <th>MODO DE TRANSPORTE</th>
            <td>{{ protocolo.concessionaria.modo_transporte.description|default:"-" }}</td>
        </tr>
        <tr>
            <th>CONCESSIONÁRIA</th>
            <td>{{ protocolo.concessionaria.nome|default:"-" }}</td>
        </tr>
        <tr>
            <th>CÓDIGO DO DOCUMENTO</th>
            <td>{{ protocolo.cod_doc|default:"-" }}</td>
        </tr>
        <tr>
            <th>DESCRIÇÃO</th>
            <td>{{ protocolo.descricao|upper|default:"-" }}</td>
        </tr>
        <tr>
            <th>PROCESSO SEI REFERÊNCIA</th>
            <td>{{ protocolo.processo_sei_ref|default:"-" }}</td>
        </tr>
        <tr>
            <th>TIPO DE IMPLANTAÇÃO</th>
            <td>{{ protocolo.tipo_obra|default:"-" }}</td>
        </tr>
        <tr>
            <th>CÓDIGO DO ITEM</th>
            <td>{{ protocolo.item_codigo|upper|default:"-" }}</td>
        </tr>
        <tr>
            <th>VALOR ESTIMADO DO DESEQUILÍBRIO</th>
            <td>{{ protocolo.valor|default:"-" }}</td>
        </tr>
        <tr>
            <th>DATA-BASE</th>
            <td>{{ protocolo.data_base|default:"-" }}</td>
        </tr>
        {% if protocolo.nova_data_pleito %}
        <tr>
            <th>NOVA DATA PLEITEADA</th>
            <td>{{ protocolo.nova_data_pleito|default:"-" }}</td>
        </tr>
        {% endif %}
        <tr>
            <th>ENDEREÇO IP</th>
            <td>{{ protocolo.ip_address|default:"-" }}</td>
        </tr>
        <tr>
            <th>HASH</th>
            <td>{{ protocolo.hash|default:"-" }}</td>
        </tr>
        <tr>
            <th>EMITIDO EM</th>
            <td>{{ protocolo.created_at|date:"d/m/Y H:i"|default:"-" }}</td>
        </tr>
    </table>
    

    <!-- Download Links Section -->
    <div id="links">
        <!-- New Print Page Button -->
        <a href="#" onclick="window.print(); return false;">Imprimir Página</a> |
        <a href="{% url 'protocolo:pdf' protocolo.uid %}">Baixar PDF</a> |
        <a href="{% url 'protocolo:arquivo' protocolo.uid %}">Download arquivo (JSON)</a> |
        <a href="{% url 'protocolo:protocolo-create' %}">Novo protocolo</a>
    </div>

    <script>
        const btnCopiaProtocolo = document.getElementById('btn__copia-protocolo');
        const txtProtocolo = document.getElementById('text__protocolo').innerText;

        btnCopiaProtocolo.addEventListener('click', function() {
            navigator.clipboard.writeText(txtProtocolo.replace(/\t/g, ''));
            alert(`Protocolo ${txtProtocolo} copiado para a área de transferência.`);
        });
    </script>
</body>
</html>
