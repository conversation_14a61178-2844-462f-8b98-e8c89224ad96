{% load static %}
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualizar Protocolo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #fff;
            color: #333;
            position: relative;
            min-height: 100vh;
        }
        
        .header {
            text-align: center;
            margin-bottom: 0px;
        }
        
        table {
            width: 60%;
            margin: 20px auto;
            border-collapse: collapse;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }
        
        th, td {
            padding: 10px;
            text-align: left;
            font-size: 12px;
            color: #333;
            border: 1px solid #ddd;
        }
        
        th {
            background-color: #000;
            color: #fff;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        td {
            background-color: #f9f9f9;
            border-bottom: 1px solid #ddd;
        }
        
        tr:last-child td {
            border-bottom: none;
        }
        
        .content-table {
            margin-top: 20px;
            margin-bottom: 120px;
        }
        
        tr:nth-child(even) td {
            background-color: #fafafa;
        }
        
        tr:nth-child(odd) td {
            background-color: #fff;
        }
        
        .header p {
            font-size: 16px;
            color: #333;
            margin: 0;
            padding: 0;
        }

        .protocol-header {
            width: 60%;
            margin: 0px auto 20px auto;
            padding: 0px 0px 20px 0px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
            text-align: center;
        }

        .protocol-number {
            font-size: 18px;
            color: #000;
            margin: 0px 0;
            padding: 0px;
            background-color: #f9f9f9;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        #links {
            position: fixed;
            left: 50%;
            bottom: 10px;
            transform: translateX(-50%);
            text-align: center;
            background-color: #da2908;
            padding: 10px 20px;
            border-radius: 25px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            z-index: 1000;
        }
        
        #links a {
            color: white;
            text-decoration: none;
            font-weight: bold;
            margin: 0 15px;
            font-size: 12px;
            transition: color 0.3s ease;
        }
        
        #links a:hover {
            color: #f1f1f1;
        }

        #btn__copia-protocolo {
            text-align: center;
            background-color: #da2908;
            padding: 5px 10px;
            border-radius: 25px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            color: white;
            text-decoration: none;
            font-weight: bold;
            margin: 15px 5px;
            font-size: 12px;
            transition: color 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        #btn__copia-protocolo:hover {
            color: #f1f1f1;
        }

        .status-badge {
            display: inline-block;
            padding: 5px 15px;
            background-color: #4CAF50;
            color: white;
            border-radius: 15px;
            font-size: 14px;
            margin-bottom: 20px;
        }

        @media print {
            #links, #btn__copia-protocolo {
                display: none;
            }
            
            body {
                padding: 20px;
            }
            
            table {
                width: 100%;
                margin: 20px 0;
            }
            
            .protocol-header {
                width: 100%;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <img src="{% static 'images/home/<USER>' %}" alt="Logo ARTESP" />
    </div>

    <div class="protocol-header">
        <div class="status-badge">Protocolo Registrado</div>
        <div class="protocol-number">
            PROTOCOLO: <strong><span id="text__protocolo">{{ protocolo.protocolo }}</span></strong>
            <button id="btn__copia-protocolo" aria-label="clique p/ copiar">copiar nro.</button>
        </div>
    </div>

    <table class="content-table">
        <tr>
            <th>NOME</th>
            <td>{{ protocolo.user_name|default:"-" }}</td>
        </tr>
        <tr>
            <th>EMAIL</th>
            <td>{{ protocolo.user_email|default:"-" }}</td>
        </tr>
        <tr>
            <th>PROTOCOLO</th>
            <td>{{ protocolo.protocolo|default:"-" }}</td>
        </tr>
        <tr>
            <th>ASSUNTO</th>
            <td>{{ protocolo.assunto.description|default:"-" }}</td>
        </tr>
        <tr>
            <th>DESTINATÁRIO</th>
            <td>{{ protocolo.destinatario.description|default:"-" }}</td>
        </tr>
        <tr>
            <th>MODO DE TRANSPORTE</th>
            <td>{{ protocolo.concessionaria.modo_transporte.description|default:"-" }}</td>
        </tr>
        <tr>
            <th>CONCESSIONÁRIA</th>
            <td>{{ protocolo.concessionaria.nome|default:"-" }}</td>
        </tr>
        <tr>
            <th>CÓDIGO DO DOCUMENTO</th>
            <td>{{ protocolo.cod_doc|default:"-" }}</td>
        </tr>
        <tr>
            <th>DESCRIÇÃO</th>
            <td>{{ protocolo.descricao|upper|default:"-" }}</td>
        </tr>
        <tr>
            <th>PROCESSO SEI REFERÊNCIA</th>
            <td>{{ protocolo.processo_sei_ref|default:"-" }}</td>
        </tr>
        <tr>
            <th>TIPO DE IMPLANTAÇÃO</th>
            <td>{{ protocolo.tipo_obra|default:"-" }}</td>
        </tr>
        <tr>
            <th>CÓDIGO DO ITEM</th>
            <td>{{ protocolo.item_codigo|upper|default:"-" }}</td>
        </tr>
        <tr>
            <th>VALOR ESTIMADO DO DESEQUILÍBRIO</th>
            <td>{{ protocolo.valor|default:"-" }}</td>
        </tr>
        <tr>
            <th>DATA-BASE</th>
            <td>{{ protocolo.data_base|default:"-" }}</td>
        </tr>
        {% if protocolo.nova_data_pleito %}
        <tr>
            <th>NOVA DATA PLEITEADA</th>
            <td>{{ protocolo.nova_data_pleito|default:"-" }}</td>
        </tr>
        {% endif %}
        <tr>
            <th>ENDEREÇO IP</th>
            <td>{{ protocolo.ip_address|default:"-" }}</td>
        </tr>
        <tr>
            <th>HASH</th>
            <td>{{ protocolo.hash|default:"-" }}</td>
        </tr>
        <tr>
            <th>EMITIDO EM</th>
            <td>{{ protocolo.created_at|date:"d/m/Y H:i"|default:"-" }}</td>
        </tr>
    </table>

    <div id="links">
        <a href="#" onclick="window.print(); return false;">Imprimir Página</a> |
        <a href="{% url 'protocolo:pdf' protocolo.uid %}">Baixar PDF</a> |
        <a href="{% url 'protocolo:arquivo' protocolo.uid %}">Download arquivo (JSON)</a>
    </div>

    <script>
        const btnCopiaProtocolo = document.getElementById('btn__copia-protocolo');
        const txtProtocolo = document.getElementById('text__protocolo').innerText;

        btnCopiaProtocolo.addEventListener('click', function() {
            navigator.clipboard.writeText(txtProtocolo.replace(/\t/g, ''));
            alert(`Protocolo ${txtProtocolo} copiado para a área de transferência.`);
        });
    </script>
</body>
</html>