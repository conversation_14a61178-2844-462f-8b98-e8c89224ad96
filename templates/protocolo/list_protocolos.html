{% load static %}
<!DOCTYPE html>
<html lang="pt-br">
<head>
  <meta charset="UTF-8" />
  <title>Lista de Protocolos</title>

  <!-- Tailwind CSS via CDN -->
  <script src="https://cdn.tailwindcss.com"></script>

  <!-- Leaflet CSS/JS -->
  <link
    rel="stylesheet"
    href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
    crossorigin=""
  />
  <script
    src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
    crossorigin=""
  ></script>

  <style>
    /* Custom styles */
    .table-header {
      background-color: #1a1a1a;
      color: #ffffff;
    }
    
    tr.hoverable:hover {
      background-color: #fee2e2;
      transition: background-color 0.2s ease;
    }

    .modal-backdrop {
      background-color: rgba(0, 0, 0, 0.75);
      backdrop-filter: blur(4px);
    }

    .filter-container.collapsed {
      display: none;
    }

    /* Smooth transitions */
    .transition-all {
      transition: all 0.3s ease;
    }

    /* Custom scrollbar */
    .custom-scrollbar::-webkit-scrollbar {
      width: 8px;
    }

    .custom-scrollbar::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 4px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
      background: #555;
    }

    /* Button styles */
    .btn-primary {
      background-color: #dc2626;
      color: white;
      transition: all 0.2s ease;
    }

    .btn-primary:hover {
      background-color: #b91c1c;
    }

    .btn-secondary {
      background-color: #1a1a1a;
      color: white;
      transition: all 0.2s ease;
    }

    .btn-secondary:hover {
      background-color: #404040;
    }

    /* Form styles for inputs and selects */
    .form-input {
      width: 100%;
      padding: 0.5rem 1rem; /* Equivalent to py-2 px-4 */
      border: 1px solid #d1d5db; /* Tailwind gray-300 */
      border-radius: 0.375rem; /* rounded-md */
      outline: none;
      transition: box-shadow 0.2s ease, border-color 0.2s ease;
      background-color: white;
    }

    .form-input:focus {
      border-color: #dc2626;
      box-shadow: 0 0 0 2px rgba(220,38,38,0.5);
    }

    /* Additional styling for select to remove native arrow and add custom arrow */
    select.form-input {
      appearance: none;
      -webkit-appearance: none;
      -moz-appearance: none;
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' stroke='%236B7280' viewBox='0 0 24 24'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'/%3E%3C/svg%3E");
      background-position: right 1rem center;
      background-repeat: no-repeat;
      background-size: 1.25rem;
    }

    .form-label {
      display: block;
      font-size: 0.875rem; /* text-sm */
      font-weight: 500; /* font-medium */
      color: #4a5568; /* text-gray-700 */
      margin-bottom: 0.25rem; /* mb-1 */
    }
  </style>
</head>
<body class="bg-gray-50 min-h-screen">
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-7xl mx-auto bg-white shadow-xl rounded-lg overflow-hidden border border-gray-200">
      <!-- Header -->
      <div class="px-6 py-4 bg-[#1a1a1a] flex justify-between items-center">
        <h1 class="text-2xl font-bold text-white">Lista de Protocolos</h1>
        <button 
          id="toggleFilters" 
          class="btn-secondary px-4 py-2 rounded-md text-sm font-medium flex items-center gap-2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd" />
          </svg>
          Filtros
        </button>
      </div>

      <!-- Filter Form -->
      <div id="filterContainer" class="p-6 border-b border-gray-200 filter-container bg-gray-50">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- Protocolo -->
          <div>
            <label for="id_protocolo" class="form-label">Protocolo</label>
            <input 
              type="text"
              name="protocolo"
              id="id_protocolo"
              value="{{ filtro_protocolo }}"
              class="form-input"
              placeholder="Protocolo"
            />
          </div>

          <!-- Concessionária -->
          <div>
            <label for="id_concessionaria" class="form-label">Concessionária</label>
            <select 
              name="concessionaria" 
              id="id_concessionaria"
              class="form-input"
            >
              <option value="">(todas)</option>
              {% for c in concessionarias_list %}
                <option value="{{ c.pk }}" {% if filtro_concessionaria == c.pk|stringformat:"s" %}selected{% endif %}>
                  L{{ c.lote|stringformat:"02d" }}-{{ c.nome }}
                </option>
              {% endfor %}
            </select>
          </div>

          <!-- Modo Transporte -->
          <div>
            <label for="id_modo_transporte" class="form-label">Modo Transporte</label>
            <select 
              name="modo_transporte" 
              id="id_modo_transporte"
              class="form-input"
            >
              <option value="">(todos)</option>
              {% for modo in modos_transporte_list %}
                <option value="{{ modo.pk }}" {% if filtro_modo_transporte == modo.pk|stringformat:"s" %}selected{% endif %}>
                  {{ modo.description }}
                </option>
              {% endfor %}
            </select>
          </div>

          <!-- Interessado -->
          <div>
            <label for="id_interessado" class="form-label">Interessado</label>
            <select 
              name="interessado" 
              id="id_interessado"
              class="form-input"
            >
              <option value="">(todos)</option>
              {% for interessado in interessados_list %}
                <option value="{{ interessado.pk }}" {% if filtro_interessado == interessado.pk|stringformat:"s" %}selected{% endif %}>
                  {{ interessado.description }}
                </option>
              {% endfor %}
            </select>
          </div>

          <!-- Destinatário -->
          <div>
            <label for="id_destinatario" class="form-label">Destinatário</label>
            <select 
              name="destinatario" 
              id="id_destinatario"
              class="form-input"
            >
              <option value="">(todos)</option>
              {% for destinatario in destinatarios_list %}
                <option value="{{ destinatario.pk }}" {% if filtro_destinatario == destinatario.pk|stringformat:"s" %}selected{% endif %}>
                  {{ destinatario.description }}
                </option>
              {% endfor %}
            </select>
          </div>

          <!-- Assunto -->
          <div>
            <label for="id_assunto" class="form-label">Assunto</label>
            <select 
              name="assunto" 
              id="id_assunto"
              class="form-input"
            >
              <option value="">(todos)</option>
              {% for assunto in assuntos_list %}
                <option value="{{ assunto.pk }}" {% if filtro_assunto == assunto.pk|stringformat:"s" %}selected{% endif %}>
                  {{ assunto.description }}
                </option>
              {% endfor %}
            </select>
          </div>

          <!-- Tipo Obra -->
          <div>
            <label for="id_tipo_obra" class="form-label">Tipo Obra</label>
            <select 
              name="tipo_obra" 
              id="id_tipo_obra"
              class="form-input"
            >
              <option value="">(todos)</option>
              {% for tipo_obra in tipos_obra_list %}
                <option value="{{ tipo_obra.pk }}" {% if filtro_tipo_obra == tipo_obra.pk|stringformat:"s" %}selected{% endif %}>
                  {{ tipo_obra.description }}
                </option>
              {% endfor %}
            </select>
          </div>

          <!-- Cód. Doc. -->
          <div>
            <label for="id_cod_doc" class="form-label">Cód. Doc.</label>
            <input 
              type="text"
              name="cod_doc"
              id="id_cod_doc"
              value="{{ filtro_cod_doc }}"
              class="form-input"
              placeholder="Cód doc"
            />
          </div>

          <!-- Rodovia -->
          <div>
            <label for="id_rodovia" class="form-label">Rodovia</label>
            <input 
              type="text"
              name="rodovia"
              id="id_rodovia"
              value="{{ filtro_rodovia }}"
              class="form-input"
              placeholder="SP 280"
            />
          </div>

          <!-- E-mail -->
          <div>
            <label for="id_user_email" class="form-label">E-mail</label>
            <input 
              type="text"
              name="user_email"
              id="id_user_email"
              value="{{ filtro_user_email }}"
              class="form-input"
              placeholder="<EMAIL>"
            />
          </div>

          <!-- Processo SEI Ref. -->
          <div>
            <label for="id_processo_sei_ref" class="form-label">Processo SEI Ref.</label>
            <input 
              type="text"
              name="processo_sei_ref"
              id="id_processo_sei_ref"
              value="{{ filtro_processo_sei_ref }}"
              class="form-input"
              placeholder="Processo SEI Ref."
            />
          </div>

          <!-- Processo SEI -->
          <div>
            <label for="id_processo_sei" class="form-label">Processo SEI</label>
            <input 
              type="text"
              name="processo_sei"
              id="id_processo_sei"
              value="{{ filtro_processo_sei }}"
              class="form-input"
              placeholder="Processo SEI"
            />
          </div>

          <!-- Criado em -->
          <div>
            <label for="id_created_at" class="form-label">Criado em</label>
            <input
              type="date"
              name="created_at"
              id="id_created_at"
              value="{{ filtro_created_at }}"
              class="form-input"
            />
          </div>

          <!-- Submit and Clear buttons -->
          <div class="col-span-full flex justify-end gap-4 mt-4">
            <a 
              href="{% url 'protocolo:list-protocolos' %}" 
              class="btn-secondary px-6 py-2 rounded-md text-sm font-medium"
            >
              Limpar Filtros
            </a>
            <button
              type="submit"
              class="btn-primary px-6 py-2 rounded-md text-sm font-medium"
            >
              Aplicar Filtros
            </button>
          </div>
        </form>
      </div>

      <!-- Table of Protocolos -->
      <div class="p-6">
        <div class="overflow-x-auto border border-gray-200 rounded-lg shadow-sm">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="table-header">
              <tr>
                <th class="px-6 py-4 text-left text-sm font-semibold uppercase tracking-wider">
                  Protocolo
                </th>
                <th class="px-6 py-4 text-left text-sm font-semibold uppercase tracking-wider">
                  Interessado
                </th>
                <th class="px-6 py-4 text-left text-sm font-semibold uppercase tracking-wider">
                  Concessionária
                </th>
                <th class="px-6 py-4 text-left text-sm font-semibold uppercase tracking-wider">
                  Criado em
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              {% for item in protocolos %}
                <tr
                  class="hoverable cursor-pointer transition-colors"
                  data-uid="{{ item.uid }}"
                  data-protocolo="{{ item.protocolo }}"
                  data-latitude="{{ item.latitude|default_if_none:'' }}"
                  data-longitude="{{ item.longitude|default_if_none:'' }}"
                  data-concessionaria="{{ item.concessionaria|default:'-' }}"
                  data-modo_transporte="{{ item.concessionaria.modo_transporte|default:'-' }}"
                  data-interessado="{{ item.interessado|default:'-' }}"
                  data-processo_sei_ref="{{ item.processo_sei_ref|default:'-' }}"
                  data-processo_sei="{{ item.processo_sei|default:'-' }}"
                  data-encaminhado_por="{{ item.encaminhado_por|default:'-' }}"
                  data-destinatario="{{ item.destinatario|default:'-' }}"
                  data-assunto="{{ item.assunto|default:'-' }}"
                  data-tipo_obra="{{ item.tipo_obra|default:'-' }}"
                  data-item_codigo="{{ item.item_codigo|default:'-' }}"
                  data-descricao="{{ item.descricao|default:'-' }}"
                  data-cod_doc="{{ item.cod_doc|default:'-' }}"
                  data-rodovia="{{ item.rodovia|default:'-' }}"
                  data-km_inicial="{{ item.km_inicial|default:'-' }}"
                  data-km_final="{{ item.km_final|default:'-' }}"
                  data-valor="{{ item.valor|floatformat:"2g"|default:'-' }}"
                  data-data_base="{{ item.data_base|default:'-' }}"
                  data-nova_data_pleito_inicio="{{ item.nova_data_pleito_inicio|date:'m-Y'|default:'-' }}"
                  data-nova_data_pleito_termino="{{ item.nova_data_pleito_termino|date:'m-Y'|default:'-' }}"
                  data-user_name="{{ item.user_name|default:'-' }}"
                  data-user_email="{{ item.user_email|default:'-' }}"
                  data-ip_address="{{ item.ip_address|default:'-' }}"
                  data-created_at="{{ item.created_at|date:'Y-m-d H:i' }}"
                  data-updated_at="{{ item.updated_at|date:'Y-m-d H:i' }}"
                >
                  <td class="px-6 py-4 whitespace-nowrap font-medium text-gray-900">
                    {{ item.protocolo }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-gray-700">
                    {{ item.interessado|default:"—" }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-gray-700">
                    {{ item.concessionaria|default:"—" }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-gray-700">
                    {{ item.created_at|date:'d/m/Y H:i' }}
                  </td>
                </tr>
              {% empty %}
                <tr>
                  <td colspan="4" class="px-6 py-8 text-center text-gray-500">
                    Nenhum protocolo encontrado.
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="mt-6 flex items-center justify-between">
          <div class="text-sm text-gray-700">
            Página {{ page_obj.number }} de {{ page_obj.paginator.num_pages }}
          </div>
          <div class="flex gap-2">
            {% if page_obj.has_previous %}
              <a 
                href="?page={{ page_obj.previous_page_number }}{% if filtro_protocolo %}&protocolo={{ filtro_protocolo }}{% endif %}{% if filtro_interessado %}&interessado={{ filtro_interessado }}{% endif %}{% if filtro_concessionaria %}&concessionaria={{ filtro_concessionaria }}{% endif %}{% if filtro_user_email %}&user_email={{ filtro_user_email }}{% endif %}{% if filtro_created_at %}&created_at={{ filtro_created_at }}{% endif %}"
                class="btn-secondary px-4 py-2 rounded-md text-sm font-medium"
              >
                Anterior
              </a>
            {% endif %}

            {% if page_obj.has_next %}
              <a 
                href="?page={{ page_obj.next_page_number }}{% if filtro_protocolo %}&protocolo={{ filtro_protocolo }}{% endif %}{% if filtro_interessado %}&interessado={{ filtro_interessado }}{% endif %}{% if filtro_concessionaria %}&concessionaria={{ filtro_concessionaria }}{% endif %}{% if filtro_user_email %}&user_email={{ filtro_user_email }}{% endif %}{% if filtro_created_at %}&created_at={{ filtro_created_at }}{% endif %}"
                class="btn-primary px-4 py-2 rounded-md text-sm font-medium"
              >
                Próximo
              </a>
            {% endif %}
          </div>
        </div>

        <!-- Download Button -->
        <button
          class="fixed bottom-8 right-8 btn-primary px-6 py-3 rounded-full shadow-lg hover:shadow-xl flex items-center gap-2"
          onclick="showDownloadModal()"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
          Download
        </button>
      </div>
    </div>
  </div>

  <!-- Download Modal -->
  <div 
    id="downloadModalBackdrop"
    class="hidden fixed inset-0 z-50 modal-backdrop flex items-center justify-center p-4"
  >
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
      <h2 class="text-xl font-bold mb-4 text-gray-900">Formato de Download</h2>
      <div class="space-y-3 mb-6">
        <label class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
          <input type="radio" name="downloadFormat" value="csv" class="h-4 w-4 text-red-600" checked />
          <span class="ml-3 text-gray-700">CSV</span>
        </label>
        <label class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
          <input type="radio" name="downloadFormat" value="excel" class="h-4 w-4 text-red-600" />
          <span class="ml-3 text-gray-700">Excel</span>
        </label>
      </div>
      <div class="flex justify-end gap-3">
        <button
          class="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
          onclick="hideDownloadModal()"
        >
          Cancelar
        </button>
        <button
          class="btn-primary px-4 py-2 rounded-md"
          onclick="triggerDownload()"
        >
          Download
        </button>
      </div>
    </div>
  </div>

  <!-- Detail Modal -->
  <div 
    id="modalBackdrop" 
    class="hidden fixed inset-0 modal-backdrop flex items-center justify-center z-50 p-4"
  >
    <div class="bg-white w-full max-w-3xl rounded-lg shadow-xl relative">
      <!-- Close Button -->
      <button 
        id="closeModalBtn"
        class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
      >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
        </svg>
      </button>

      <!-- Modal Content -->
      <div class="custom-scrollbar overflow-y-auto" style="max-height: 85vh;">
        <!-- Modal Header -->
        <div class="bg-[#1a1a1a] px-6 py-4">
          <h2 id="modalTitle" class="text-xl font-bold text-white"></h2>
        </div>

        <!-- Info Section -->
        <div class="p-6 space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <p><strong>Interessado:</strong> <span id="modalInteressado"></span></p>
            <p><strong>Concessionária:</strong> <span id="modalConcessionaria"></span></p>
            <p><strong>Modo de Transporte:</strong> <span id="modalModoTransporte"></span></p>
            <p><strong>Destinatário:</strong> <span id="modalDestinatario"></span></p>
            <p><strong>Assunto:</strong> <span id="modalAssunto"></span></p>
            <p><strong>Tipo de Implantação:</strong> <span id="modalTipoObra"></span></p>
            <p><strong>Item código:</strong> <span id="modalItemCodigo"></span></p>
            <p><strong>Rodovia:</strong> <span id="modalRodovia"></span></p>
            <p><strong>KM inicial:</strong> <span id="modalKmInicial"></span></p>
            <p><strong>KM final:</strong> <span id="modalKmFinal"></span></p>
            <p><strong>Latitude:</strong> <span id="modalLat"></span></p>
            <p><strong>Longitude:</strong> <span id="modalLon"></span></p>
            <p><strong>Valor (R$):</strong> <span id="modalValor"></span></p>
            <p><strong>Data-base:</strong> <span id="modalDataBase"></span></p>
            <p><strong>Data início pleiteada:</strong> <span id="modalDataInicio"></span></p>
            <p><strong>Data término pleiteada:</strong> <span id="modalDataTermino"></span></p>
            <p><strong>Processo SEI Ref:</strong> <span id="modalProcessoSeiRef"></span></p>
            <p><strong>Processo SEI:</strong> <span id="modalProcessoSei"></span></p>
            <p><strong>Descrição:</strong> <span id="modalDescricao"></span></p>
            <p><strong>Código do Documento:</strong> <span id="modalCodDoc"></span></p>
            <p><strong>Nome Usuário:</strong> <span id="modalUserName"></span></p>
            <p><strong>Email Usuário:</strong> <span id="modalUserEmail"></span></p>
            <p><strong>End. IP:</strong> <span id="modalIpAddress"></span></p>
            <p><strong>Criado em:</strong> <span id="modalCreatedAt"></span></p>
            <p><strong>Atualizado em:</strong> <span id="modalUpdatedAt"></span></p>
          </div>

          <!-- Baixar PDF Button -->
          <div class="mt-8 flex justify-center">
            <a 
              class="btn-primary px-6 py-3 rounded-md flex items-center gap-2"
              id="pdfDownloadButton"
              href="#"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586L7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clip-rule="evenodd" />
              </svg>
              Baixar PDF
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script>
    document.getElementById('toggleFilters').addEventListener('click', function() {
      const filterContainer = document.getElementById('filterContainer');
      filterContainer.classList.toggle('collapsed');
    });

    /*********************** INFO MODAL ***************************/
    const modalBackdrop = document.getElementById('modalBackdrop');
    const closeModalBtn = document.getElementById('closeModalBtn');

    function closeModal() {
      modalBackdrop.classList.add('hidden');
    }

    function openModal(row) {
      const uid = row.dataset.uid || '';
      const protocolo = row.dataset.protocolo || '';
      const concessionaria = row.dataset.concessionaria || '';
      const modo_transporte = row.dataset.modo_transporte || '';
      const interessado = row.dataset.interessado || '';
      const processo_sei_ref = row.dataset.processo_sei_ref || '';
      const processo_sei = row.dataset.processo_sei || '';
      const destinatario = row.dataset.destinatario || '';
      const assunto = row.dataset.assunto || '';
      const tipo_obra = row.dataset.tipo_obra || '';
      const item_codigo = row.dataset.item_codigo || '';
      const rodovia = row.dataset.rodovia || '';
      const km_inicial = row.dataset.km_inicial || '';
      const km_final = row.dataset.km_final || '';
      const latitude = row.dataset.latitude || '';
      const longitude = row.dataset.longitude || '';
      const descricao = row.dataset.descricao || '';
      const cod_doc = row.dataset.cod_doc || '';
      const valor = row.dataset.valor || '';
      const data_base = row.dataset.data_base || '';
      const nova_data_pleito_inicio = row.dataset.nova_data_pleito_inicio || '';
      const nova_data_pleito_termino = row.dataset.nova_data_pleito_termino || '';
      const user_name = row.dataset.user_name || '';
      const user_email = row.dataset.user_email || '';
      const ip_address = row.dataset.ip_address || '';
      const created_at = row.dataset.created_at || '';
      const updated_at = row.dataset.updated_at || '';
      
      document.getElementById('modalTitle').textContent = `Protocolo: ${protocolo}`;
      document.getElementById('modalInteressado').textContent = interessado;
      document.getElementById('modalConcessionaria').textContent = concessionaria;
      document.getElementById('modalModoTransporte').textContent = modo_transporte;
      document.getElementById('modalDestinatario').textContent = destinatario;
      document.getElementById('modalAssunto').textContent = assunto;
      document.getElementById('modalTipoObra').textContent = tipo_obra;
      document.getElementById('modalItemCodigo').textContent = item_codigo;
      document.getElementById('modalRodovia').textContent = rodovia;
      document.getElementById('modalKmInicial').textContent = km_inicial;
      document.getElementById('modalKmFinal').textContent = km_final;
      document.getElementById('modalLat').textContent = latitude;
      document.getElementById('modalLon').textContent = longitude;
      document.getElementById('modalValor').textContent = valor;
      document.getElementById('modalDataBase').textContent = data_base;
      document.getElementById('modalDataInicio').textContent = nova_data_pleito_inicio;
      document.getElementById('modalDataTermino').textContent = nova_data_pleito_termino;
      document.getElementById('modalProcessoSeiRef').textContent = processo_sei_ref;
      document.getElementById('modalProcessoSei').textContent = processo_sei;
      document.getElementById('modalDescricao').textContent = descricao;
      document.getElementById('modalCodDoc').textContent = cod_doc;
      document.getElementById('modalUserName').textContent = user_name;
      document.getElementById('modalUserEmail').textContent = user_email;
      document.getElementById('modalIpAddress').textContent = ip_address;
      document.getElementById('modalCreatedAt').textContent = created_at;
      document.getElementById('modalUpdatedAt').textContent = updated_at;

      modalBackdrop.classList.remove('hidden');

      const pdfDownloadButton = document.getElementById('pdfDownloadButton')
      if (uid) {
        pdfDownloadLink = `{% url 'protocolo:download-pdf-modal' protocolo_uid="76bde489-de19-4864-a2e8-cfc9171e49e2" %}`
                            .replace('76bde489-de19-4864-a2e8-cfc9171e49e2', uid.toString())
        pdfDownloadButton.href = pdfDownloadLink;
        pdfDownloadButton.classList.remove('hidden');
      } else {
        pdfDownloadButton.classList.add('hidden');
      }
    }

    document.querySelectorAll(' tbody tr.hoverable').forEach((row) => {
      row.addEventListener('click', () => openModal(row));
    });
    closeModalBtn.addEventListener('click', closeModal);
    modalBackdrop.addEventListener('click', (e) => {
      if (e.target === modalBackdrop) {
        closeModal();
      }
    });

    /*********************** DOWNLOAD MODAL ***************************/
    const downloadModalBackdrop = document.getElementById('downloadModalBackdrop');

    function showDownloadModal() {
      downloadModalBackdrop.classList.remove('hidden');
    }
    function hideDownloadModal() {
      downloadModalBackdrop.classList.add('hidden');
    }

    function triggerDownload() {
      const selectedFormat = document.querySelector('input[name="downloadFormat"]:checked').value;
      const currentUrl = new URL(window.location.href);
      // Add export_format param
      currentUrl.searchParams.set('export_format', selectedFormat);

      // Force the browser to navigate => triggers the download response
      window.location = currentUrl.toString();

      hideDownloadModal();
    }
  </script>
</body>
</html>
