<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Protocolo SEI Atualizado</title>
    <style>
        /* Email-safe CSS styles */
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            background-color: #000000;
            color: #ffffff;
            padding: 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        .update-banner {
            background-color: #e7f3ff;
            border: 1px solid #b8daff;
            color: #004085;
            padding: 15px;
            margin: 20px;
            border-radius: 5px;
            text-align: center;
        }
        .update-banner h2 {
            margin: 0 0 10px 0;
            font-size: 20px;
        }
        .content {
            padding: 20px;
        }
        .doc-code-highlight {
            background-color: #fff5f5;
            border: 2px solid #fed7d7;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }
        .doc-code {
            font-size: 28px;
            font-weight: bold;
            color: #FF161F;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        .change-highlight {
            background-color: #fff3cd;
            border: 2px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .change-highlight h3 {
            margin: 0 0 15px 0;
            color: #856404;
            font-size: 18px;
        }
        .change-comparison {
            display: table;
            width: 100%;
            margin: 15px 0;
        }
        .change-row {
            display: table-row;
        }
        .change-cell {
            display: table-cell;
            padding: 10px;
            border: 1px solid #ddd;
            vertical-align: middle;
        }
        .change-label {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
            width: 30%;
        }
        .old-value {
            background-color: #f8d7da;
            color: #721c24;
            font-family: 'Courier New', monospace;
        }
        .new-value {
            background-color: #d4edda;
            color: #155724;
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
        .info-grid {
            display: table;
            width: 100%;
            margin: 20px 0;
        }
        .info-row {
            display: table-row;
        }
        .info-cell {
            display: table-cell;
            padding: 8px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: top;
        }
        .info-label {
            font-weight: bold;
            color: #495057;
            width: 40%;
        }
        .info-value {
            color: #212529;
        }
        .section {
            margin: 25px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .section h3 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 18px;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 5px;
        }
        .footer {
            background-color: #000000;
            color: #ffffff;
            padding: 20px;
            text-align: center;
            font-size: 14px;
        }
        .access-note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .access-note strong {
            color: #002752;
        }
        @media only screen and (max-width: 600px) {
            .container {
                margin: 0;
                border-radius: 0;
            }
            .info-grid, .change-comparison {
                display: block;
            }
            .info-row, .change-row {
                display: block;
                margin-bottom: 10px;
            }
            .info-cell, .change-cell {
                display: block;
                padding: 5px 0;
            }
            .doc-code {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>ARTESP - Sistema GERPRO</h1>
            <p>Protocolo SEI Atualizado</p>
        </div>

        <!-- Update Banner -->
        <div class="update-banner">
            <h2>✅ Protocolo Atualizado com Sucesso!</h2>
            <p>As alterações no seu protocolo foram salvas e estão disponíveis abaixo.</p>
        </div>

        <!-- Main Content -->
        <div class="content">
            <!-- Document Code Highlight -->
            <div class="doc-code-highlight">
                <h3 style="margin: 0 0 10px 0; color: #495057;">Código do Documento</h3>
                <div class="doc-code">{{ doc_cod }}</div>
                <p style="margin: 10px 0 0 0; color: #6c757d; font-size: 14px;">
                    Protocolo atualizado em {{ update_info.updated_at|date:"d/m/Y H:i" }}
                </p>
            </div>

            <!-- Change Highlight -->
            <div class="change-highlight">
                <h3>🔄 Alteração Realizada</h3>
                <p style="margin: 0 0 15px 0; color: #856404;">
                    <strong>Campo alterado:</strong> {{ update_info.field_name }}
                </p>
                
                <div class="change-comparison">
                    <div class="change-row">
                        <div class="change-cell change-label">Valor Anterior:</div>
                        <div class="change-cell old-value">{{ update_info.old_value }}</div>
                    </div>
                    <div class="change-row">
                        <div class="change-cell change-label">Novo Valor:</div>
                        <div class="change-cell new-value">{{ update_info.new_value }}</div>
                    </div>
                </div>
                
                <p style="margin: 15px 0 0 0; color: #856404; font-size: 14px;">
                    <strong>Data da Alteração:</strong> {{ update_info.updated_at|date:"d/m/Y H:i" }}
                </p>
            </div>

            <!-- Requisitante Information -->
            <div class="section">
                <h3>👤 Requisitante</h3>
                <div class="info-grid">
                    <div class="info-row">
                        <div class="info-cell info-label">Nome:</div>
                        <div class="info-cell info-value">{{ usuario_nome }}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-cell info-label">E-mail:</div>
                        <div class="info-cell info-value">{{ usuario_email }}</div>
                    </div>
                </div>
            </div>

            <!-- Protocol Information Summary -->
            <div class="section">
                <h3>📋 Informações do Protocolo</h3>
                <div class="info-grid">
                    <div class="info-row">
                        <div class="info-cell info-label">Unidade:</div>
                        <div class="info-cell info-value">{{ unidade.unidade }}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-cell info-label">Serviço:</div>
                        <div class="info-cell info-value">{{ servico.tipo }}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-cell info-label">Assunto:</div>
                        <div class="info-cell info-value">{{ assunto.descricao }}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-cell info-label">Data de Criação:</div>
                        <div class="info-cell info-value">{{ created_at|date:"d/m/Y H:i" }}</div>
                    </div>
                </div>
            </div>

            <!-- Access Information -->
            <div class="access-note">
                <strong>🔐 Acesso para Futuras Edições</strong><br>
                Sua senha de acesso continua válida: <span style="font-family: 'Courier New', monospace; font-weight: bold;">{{ access_password }}</span><br>
                <small>Esta senha permite editar apenas o campo "Número SEI" e é válida por 7 dias a partir da criação do protocolo.</small>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>© {{ current_year }} ARTESP - Sistema GERPRO - Todos os direitos reservados</p>
            <p style="font-size: 12px; margin-top: 10px;">
                Este é um e-mail automático de confirmação de atualização. Por favor, não responda a esta mensagem.
            </p>
        </div>
    </div>
</body>
</html>
